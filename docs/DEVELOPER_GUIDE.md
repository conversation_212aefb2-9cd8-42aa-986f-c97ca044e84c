# 🔧 Guide développeur - Binou Compta Perso

## 🏗️ Architecture technique

### Stack technologique
- **Frontend** : React 18 + Vite
- **Backend** : Node.js + Express
- **Base de données** : SQLite3
- **Authentification** : Token-based (localStorage)
- **Styling** : CSS3 avec variables CSS

### Structure des dossiers
```
MaComptaPerso/
├── client/                     # Frontend React
│   ├── public/                 # Assets statiques
│   ├── src/
│   │   ├── components/         # Composants React
│   │   │   ├── BalanceCard.jsx
│   │   │   ├── CategoryManager.jsx
│   │   │   ├── Dashboard.jsx
│   │   │   ├── LoginForm.jsx
│   │   │   ├── Navigation.jsx
│   │   │   ├── StatsCard.jsx
│   │   │   ├── Toast.jsx
│   │   │   ├── TransactionForm.jsx
│   │   │   └── TransactionList.jsx
│   │   ├── App.jsx             # Composant principal
│   │   ├── main.jsx            # Point d'entrée
│   │   └── styles.css          # Styles globaux
│   ├── package.json
│   └── vite.config.js
├── server/                     # Backend Node.js
│   ├── data/                   # Base de données SQLite
│   ├── db.js                   # Configuration base de données
│   ├── index.js                # Serveur Express
│   └── package.json
└── docs/                       # Documentation
```

## 🔧 Configuration de développement

### Prérequis
```bash
# Node.js version recommandée
node --version  # >= 16.0.0
npm --version   # >= 8.0.0
```

### Installation
```bash
# Cloner et installer
git clone <repo>
cd MaComptaPerso

# Dépendances serveur
cd server && npm install

# Dépendances client
cd ../client && npm install
```

### Variables d'environnement
Créer `.env` dans `/server` :
```env
PORT=3333
NODE_ENV=development
DB_PATH=./data/database.sqlite
```

## 🚀 Développement

### Démarrage rapide
```bash
# Mode développement avec rechargement auto
./dev.sh

# Ou manuellement
cd server && nodemon index.js &
cd client && npm run dev &
```

### Scripts utiles
```bash
./restart-all.sh      # Redémarrage complet
./stop-all.sh         # Arrêt propre
tail -f server.log    # Logs serveur
tail -f client.log    # Logs client
```

## 📊 Base de données

### Schéma SQLite
```sql
-- Table des utilisateurs
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Table des catégories
CREATE TABLE categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  user_id INTEGER NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Table des transactions
CREATE TABLE transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  amount REAL NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('in', 'out')),
  description TEXT,
  category_id INTEGER,
  user_id INTEGER NOT NULL,
  date_iso DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories (id),
  FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### Requêtes courantes
```javascript
// Récupérer le solde
const getBalance = (userId, callback) => {
  db.get(`
    SELECT 
      COALESCE(SUM(CASE WHEN type = 'in' THEN amount ELSE 0 END), 0) as income,
      COALESCE(SUM(CASE WHEN type = 'out' THEN amount ELSE 0 END), 0) as expenses
    FROM transactions 
    WHERE user_id = ?
  `, [userId], callback);
};
```

## 🔌 API Endpoints

### Authentification
```javascript
POST /api/login          // Connexion
POST /api/logout         // Déconnexion
GET  /api/me            // Infos utilisateur
```

### Transactions
```javascript
GET    /api/transactions     // Liste des transactions
POST   /api/transactions     // Créer une transaction
DELETE /api/transactions/:id // Supprimer une transaction
GET    /api/balance         // Obtenir le solde
```

### Catégories
```javascript
GET    /api/categories              // Liste des catégories
POST   /api/categories              // Créer une catégorie
PUT    /api/categories/:id          // Modifier une catégorie
DELETE /api/categories/:id          // Supprimer une catégorie
POST   /api/categories/init-defaults // Initialiser catégories par défaut
```

### Middleware d'authentification
```javascript
const requireAuth = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token || !sessions.has(token)) {
    return res.status(401).json({ error: 'Non authentifié' });
  }
  req.user = sessions.get(token);
  next();
};
```

## ⚛️ Composants React

### Structure des composants
```javascript
// Exemple : TransactionForm.jsx
import React, { useState } from 'react'

export default function TransactionForm({ 
  categories, 
  onSaved, 
  apiBase, 
  authToken 
}) {
  const [formData, setFormData] = useState({
    amount: '',
    type: 'out',
    category_id: '',
    description: ''
  })

  // Logique du composant...
  
  return (
    <form onSubmit={handleSubmit}>
      {/* JSX du formulaire */}
    </form>
  )
}
```

### Gestion d'état
- **useState** pour l'état local des composants
- **Props drilling** pour passer les données
- **Callbacks** pour la communication parent-enfant

### Patterns utilisés
- **Controlled components** pour les formulaires
- **Conditional rendering** pour l'affichage conditionnel
- **Event handling** avec async/await
- **Error boundaries** pour la gestion d'erreurs

## 🎨 Styling

### Variables CSS
```css
:root {
  --primary-color: #1976d2;
  --primary-dark: #1565c0;
  --success-color: #4caf50;
  --danger-color: #f44336;
  --warning-color: #ff9800;
  --text-primary: #212121;
  --text-secondary: #757575;
  --background: #fafafa;
  --border-color: #e0e0e0;
  --border-radius: 8px;
  --shadow: 0 2px 8px rgba(0,0,0,0.1);
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}
```

### Responsive design
```css
@media (max-width: 768px) {
  .nav-menu {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .nav-label {
    display: none;
  }
}
```

## 🧪 Tests

### Tests unitaires (à implémenter)
```bash
# Frontend
cd client
npm test

# Backend
cd server
npm test
```

### Tests manuels
1. **Authentification** : Login/logout
2. **CRUD transactions** : Créer, lire, supprimer
3. **CRUD catégories** : Créer, lire, supprimer
4. **Responsive** : Tester sur mobile
5. **Erreurs** : Tester les cas d'erreur

## 🚀 Déploiement

### Build de production
```bash
# Client
cd client
npm run build

# Serveur (déjà prêt)
cd server
npm start
```

### Variables de production
```env
NODE_ENV=production
PORT=3333
DB_PATH=/var/data/database.sqlite
```

## 🐛 Debugging

### Logs serveur
```bash
# Logs en temps réel
tail -f server.log

# Erreurs uniquement
tail -f server.log | grep ERROR
```

### Debug React
```javascript
// Console.log dans les composants
console.log('State:', formData)

// React DevTools (extension navigateur)
// Inspecter les props et state
```

### Debug base de données
```bash
# Ouvrir SQLite
sqlite3 server/data/database.sqlite

# Requêtes de debug
.tables
SELECT * FROM users;
SELECT * FROM categories LIMIT 5;
SELECT * FROM transactions ORDER BY date_iso DESC LIMIT 10;
```

## 🔄 Contribution

### Workflow Git
```bash
# Créer une branche
git checkout -b feature/nouvelle-fonctionnalite

# Développer et tester
./dev.sh

# Commit et push
git add .
git commit -m "feat: ajouter nouvelle fonctionnalité"
git push origin feature/nouvelle-fonctionnalite
```

### Standards de code
- **ESLint** pour JavaScript
- **Prettier** pour le formatage
- **Conventional Commits** pour les messages
- **Tests** obligatoires pour les nouvelles fonctionnalités

---

**🔧 Questions techniques ?** Consultez les logs ou créez une issue sur le repo.
