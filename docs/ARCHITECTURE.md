# 🏗️ Architecture - Binou Compta Perso

## 📋 Vue d'ensemble

**Binou Compta Perso** est une application web full-stack moderne suivant une architecture client-serveur avec une API RESTful.

```
┌─────────────────┐    HTTP/JSON    ┌─────────────────┐    SQLite    ┌─────────────┐
│   React Client  │ ◄──────────────► │  Express Server │ ◄───────────► │  Database   │
│   (Frontend)    │                 │   (Backend)     │              │   (SQLite)  │
└─────────────────┘                 └─────────────────┘              └─────────────┘
```

## 🎯 Principes architecturaux

### Séparation des responsabilités
- **Frontend** : Interface utilisateur et expérience
- **Backend** : Logique métier et API
- **Database** : Persistance des données

### Architecture RESTful
- Endpoints standardisés (`GET`, `POST`, `PUT`, `DELETE`)
- Stateless (sans état côté serveur)
- Authentification par token

### Responsive Design
- Mobile-first approach
- Progressive enhancement
- Accessibilité intégrée

## 🖥️ Frontend (React)

### Architecture des composants
```
App.jsx (Root)
├── Navigation.jsx
├── LoginForm.jsx (si non connecté)
└── Main Content (si connecté)
    ├── Dashboard.jsx
    │   ├── BalanceCard.jsx
    │   └── StatsCard.jsx
    ├── TransactionForm.jsx
    ├── TransactionList.jsx
    ├── CategoryManager.jsx
    └── Toast.jsx
```

### Flux de données
```
User Action → Component State → API Call → Server Response → State Update → UI Re-render
```

### Gestion d'état
- **Local State** : `useState` pour les composants
- **Props** : Communication parent-enfant
- **Context** : Authentification globale (potentiel)
- **LocalStorage** : Persistance du token

### Patterns utilisés
- **Container/Presentational** : Séparation logique/affichage
- **Controlled Components** : Formulaires contrôlés
- **Conditional Rendering** : Affichage conditionnel
- **Error Boundaries** : Gestion d'erreurs

## 🔧 Backend (Node.js + Express)

### Architecture en couches
```
┌─────────────────────┐
│   Routes Layer      │  ← Endpoints HTTP
├─────────────────────┤
│   Middleware Layer  │  ← Auth, CORS, Parsing
├─────────────────────┤
│   Business Layer    │  ← Logique métier
├─────────────────────┤
│   Data Access Layer │  ← Requêtes DB
└─────────────────────┘
```

### Middleware Stack
```javascript
app.use(cors())                    // CORS
app.use(bodyParser.json())         // JSON parsing
app.use(requireAuth)               // Authentication (routes protégées)
```

### Gestion des sessions
```javascript
const sessions = new Map()  // Token → User mapping
// Token stocké côté client (localStorage)
// Validation à chaque requête protégée
```

### Patterns utilisés
- **MVC** : Séparation Modèle-Vue-Contrôleur
- **Middleware Pattern** : Chaîne de traitement
- **Repository Pattern** : Abstraction d'accès aux données
- **Error Handling** : Gestion centralisée des erreurs

## 🗄️ Base de données (SQLite)

### Modèle de données
```sql
Users (1) ──────── (N) Categories
  │                      │
  │                      │
  └── (1) ──────── (N) Transactions ──── (N) Categories
```

### Schéma relationnel
```
users
├── id (PK)
├── username (UNIQUE)
├── password_hash
└── created_at

categories
├── id (PK)
├── name
├── user_id (FK → users.id)
└── created_at

transactions
├── id (PK)
├── amount
├── type ('in'|'out')
├── description
├── category_id (FK → categories.id)
├── user_id (FK → users.id)
└── date_iso
```

### Contraintes d'intégrité
- **Clés étrangères** : Relations entre tables
- **Check constraints** : Validation des types
- **Unique constraints** : Unicité des usernames
- **Not null** : Champs obligatoires

## 🔐 Sécurité

### Authentification
```
1. Login → Vérification credentials
2. Génération token unique
3. Stockage token (Map côté serveur, localStorage côté client)
4. Validation token à chaque requête
```

### Protection des données
- **Isolation utilisateur** : Toutes les requêtes filtrées par `user_id`
- **Validation input** : Sanitisation des données
- **SQL Injection** : Requêtes préparées
- **XSS Protection** : Échappement des données

### Bonnes pratiques
- Tokens expirables (à implémenter)
- Hachage des mots de passe (bcrypt)
- HTTPS en production
- Rate limiting (à implémenter)

## 🌐 Communication Client-Serveur

### Format des échanges
```javascript
// Requête
{
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify(data)
}

// Réponse succès
{
  ok: true,
  data: {...}
}

// Réponse erreur
{
  error: "Message d'erreur"
}
```

### Gestion d'erreurs
```
Client Error (4xx) → Affichage message utilisateur
Server Error (5xx) → Log + message générique
Network Error → Retry + message connexion
```

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile first */
@media (max-width: 768px) {
  /* Adaptations mobile */
}

@media (min-width: 769px) {
  /* Desktop */
}
```

### Stratégie d'adaptation
- **Navigation** : Menu horizontal → icônes verticaux
- **Formulaires** : Colonnes → lignes empilées
- **Tableaux** : Scroll horizontal + taille réduite
- **Cards** : Grid responsive

## 🚀 Performance

### Frontend
- **Vite** : Build tool rapide
- **Code splitting** : Chargement à la demande
- **Lazy loading** : Composants différés
- **Memoization** : Optimisation re-renders

### Backend
- **SQLite** : Base de données embarquée rapide
- **Connection pooling** : Réutilisation connexions
- **Caching** : Mise en cache des requêtes fréquentes
- **Compression** : Gzip des réponses

### Base de données
- **Index** : Sur les colonnes fréquemment requêtées
- **Requêtes optimisées** : JOINs efficaces
- **Pagination** : Limitation des résultats
- **Transactions** : Cohérence des données

## 🔄 Évolutivité

### Améliorations possibles
- **State Management** : Redux/Zustand pour état complexe
- **Real-time** : WebSockets pour mises à jour live
- **PWA** : Application web progressive
- **Tests** : Jest + React Testing Library
- **CI/CD** : Pipeline d'intégration continue
- **Docker** : Containerisation
- **Monitoring** : Logs et métriques

### Scalabilité
- **Database** : Migration vers PostgreSQL
- **Caching** : Redis pour les sessions
- **Load Balancing** : Répartition de charge
- **Microservices** : Découpage en services

## 📊 Diagrammes

### Flux d'authentification
```
User → LoginForm → API /login → Token → localStorage → Authenticated State
```

### Flux de transaction
```
User → TransactionForm → Validation → API /transactions → Database → Response → UI Update
```

### Architecture de déploiement
```
Internet → Reverse Proxy → Node.js Server → SQLite Database
                        → Static Files (React Build)
```

---

**🏗️ Cette architecture garantit maintenabilité, sécurité et évolutivité du projet.**
