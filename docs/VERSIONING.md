# 🏷️ Système de Versioning - Ma Compta Perso

Ce document explique comment utiliser le système de versioning automatisé de l'application.

## 📋 Vue d'ensemble

Le système de versioning utilise :
- **Semantic Versioning** (semver) : `MAJOR.MINOR.PATCH`
- **Génération automatique** de changelog
- **Release notes** automatiques sur GitHub
- **Affichage de version** dans l'interface utilisateur

## 🚀 Utilisation

### Méthode 1 : Script de release (Recommandé)

```bash
# Release patch (1.0.0 → 1.0.1)
./release.sh patch

# Release minor (1.0.0 → 1.1.0)
./release.sh minor

# Release major (1.0.0 → 2.0.0)
./release.sh major
```

### Méthode 2 : Commandes NPM

```bash
# Release patch
npm run version:patch

# Release minor
npm run version:minor

# Release major
npm run version:major
```

## 📝 Convention de commits

Pour des release notes optimales, utilisez ces préfixes :

- `feat:` ou `feature:` → Nouvelles fonctionnalités ✨
- `fix:` ou `bug:` → Corrections de bugs 🐛
- `docs:` → Documentation 📚
- `style:` → Formatage, style 💄
- `refactor:` → Refactoring 🔨
- `test:` → Tests 🧪
- `chore:` → Maintenance 🔧

### Exemples :
```bash
git commit -m "feat: ajout du système de versioning"
git commit -m "fix: correction du double affichage des icônes"
git commit -m "docs: mise à jour du guide utilisateur"
```

## 🔄 Processus automatique

### Lors d'une release :

1. **Mise à jour des versions** dans tous les package.json
2. **Génération du changelog** basé sur les commits
3. **Création du fichier version.json** pour le client
4. **Commit et tag** automatiques
5. **Push vers GitHub** (avec confirmation)
6. **GitHub Actions** crée la release avec notes

### GitHub Actions :

- **Auto Changelog** : Met à jour le changelog sur chaque push
- **Release** : Crée une release GitHub avec artifacts

## 📁 Fichiers générés

```
├── CHANGELOG.md              # Historique des versions
├── client/public/version.json # Info version pour le client
├── scripts/
│   ├── update-versions.js    # Sync des versions
│   └── generate-changelog.js # Génération changelog
└── .github/workflows/        # GitHub Actions
    ├── release.yml
    └── auto-changelog.yml
```

## 🖥️ Affichage dans l'interface

La version s'affiche automatiquement :
- **Page de connexion** : Remplace les infos de login
- **Format** : Version + date de build
- **Source** : Fichier `/version.json`

## 🛠️ Scripts disponibles

```bash
# Développement avec les deux serveurs
npm run dev

# Build de production
npm run build

# Installation complète
npm run install:all

# Mise à jour manuelle des versions
npm run update-versions

# Génération manuelle du changelog
npm run generate-changelog
```

## 🔧 Configuration

### Variables d'environnement (GitHub Actions) :

- `GITHUB_TOKEN` : Token pour créer les releases
- `GITHUB_SHA` : Hash du commit (automatique)

### Personnalisation :

- **Format du changelog** : Modifier `scripts/generate-changelog.js`
- **Workflow GitHub** : Modifier `.github/workflows/release.yml`
- **Affichage version** : Modifier `client/src/components/LoginForm.jsx`

## 📊 Exemple de workflow

```bash
# 1. Développement
git add .
git commit -m "feat: nouvelle fonctionnalité de filtrage"

# 2. Release
./release.sh minor

# 3. Résultat automatique :
# - Version 1.1.0 créée
# - Changelog mis à jour
# - Tag git créé
# - Release GitHub générée
# - Version affichée dans l'app
```

## 🐛 Dépannage

### Problème : Version non affichée dans l'app
```bash
# Vérifier le fichier version.json
cat client/public/version.json

# Régénérer si nécessaire
npm run update-versions
```

### Problème : Changelog vide
```bash
# Vérifier les commits
git log --oneline --no-merges

# Régénérer manuellement
npm run generate-changelog
```

### Problème : GitHub Actions échoue
- Vérifier les permissions du token GitHub
- Vérifier la syntaxe des workflows YAML
- Consulter les logs dans l'onglet Actions

## 📚 Ressources

- [Semantic Versioning](https://semver.org/)
- [Keep a Changelog](https://keepachangelog.com/)
- [GitHub Actions](https://docs.github.com/en/actions)
