# 📋 Guide d'utilisation - Binou Compta Perso

## 🎯 Premiers pas

### Connexion à l'application

1. Ouvrez votre navigateur sur http://localhost:5173
2. Utilisez les identifiants par défaut :
   - **Utilisateur** : `admin`
   - **Mot de passe** : `password`
3. C<PERSON><PERSON> sur "Se connecter"

### Interface principale

Après connexion, vous accédez à une interface moderne avec :
- **Menu de navigation** en haut avec 4 sections
- **Contenu principal** qui change selon la section active
- **Notifications** en bas à droite pour les confirmations

## 📊 Dashboard

Le dashboard est votre page d'accueil. Il affiche :

### Carte de solde
- **Revenus totaux** (en vert)
- **Dépenses totales** (en rouge)  
- **Solde actuel** (vert si positif, rouge si négatif)

### Statistiques
- Nombre total de transactions
- Revenu moyen par transaction
- Dépense moyenne par transaction
- Catégorie la plus utilisée

### Transactions récentes
- Les 5 dernières transactions passées/présentes
- Avec montant, catégorie et date
- Lien vers l'historique complet

### Transactions à venir (🔮)
- Section séparée pour les transactions futures
- Affichage avec style distinctif (transparence)
- Note explicative sur l'exclusion du solde actuel
- Tri par date croissante (prochaines en premier)

## ➕ Ajouter une transaction

### Accès
Cliquez sur "➕ Ajouter Transaction" dans le menu.

### Formulaire
1. **Date** : Sélectionnez la date de la transaction
   - Par défaut : date du jour
   - **Dates passées** : Autorisées (ex: restaurant d'hier)
   - **Dates futures** : Autorisées (ex: prélèvement prévu)
2. **Montant** : Saisissez le montant (ex: 50.99)
3. **Type** :
   - "Revenu" pour les entrées d'argent
   - "Dépense" pour les sorties d'argent
4. **Catégorie** : Choisissez dans la liste déroulante
5. **Description** : Ajoutez un commentaire (optionnel)
6. Cliquez sur "Ajouter la transaction"

### Après ajout
- Notification de confirmation
- Retour automatique au dashboard
- Mise à jour des statistiques

## 🏷️ Gestion des catégories

### Accès
Cliquez sur "🏷️ Catégories" dans le menu.

### Catégories par défaut
Au premier lancement, cliquez sur "✨ Ajouter les catégories par défaut" pour obtenir 20 catégories courantes :
- 🏠 Logement
- 🚗 Transport
- 🍽️ Alimentation
- 📱 Téléphone
- Et 16 autres...

### Ajouter une catégorie

#### Méthode rapide avec sélecteur d'emoji
1. **Cliquez sur le bouton emoji** (😀) à côté du champ de saisie
2. **Choisissez un emoji** dans la liste organisée par catégories :
   - 💰 Argent & Finance
   - 🏠 Maison & Logement
   - 🚗 Transport
   - 🍕 Alimentation
   - 🛒 Shopping & Achats
   - Et 9 autres catégories avec plus de 500 emojis
3. **Saisissez le nom** de la catégorie (l'emoji est déjà ajouté)
4. **Cliquez sur "✅ Ajouter"**

#### Méthode manuelle
1. Dans le champ "Nom de la catégorie"
2. Tapez le nom avec emoji (ex: "🎮 Jeux vidéo")
3. Cliquez sur "✅ Ajouter"

### Supprimer une catégorie
1. Trouvez la catégorie dans la liste
2. Cliquez sur l'icône 🗑️
3. Confirmez la suppression

⚠️ **Attention** : La suppression est définitive !

## 📋 Historique des transactions

### Accès
Cliquez sur "📋 Historique" dans le menu.

### Affichage des transactions
- **Tableau complet** avec colonnes : Date, Montant, Type, Catégorie, Note, Pointage, Actions
- **Tri par défaut** : Date décroissante (plus récentes en premier)
- **Badges colorés** : vert pour revenus, rouge pour dépenses
- **Statut de pointage** : ✅ Pointée ou ⏳ À pointer
- **Transactions futures** : Masquées par défaut avec option d'affichage

### 🔄 Système de tri avancé

#### Tri par colonnes
Cliquez sur **n'importe quel en-tête de colonne** pour trier :
- **Date** ↕️ : Chronologique (récent ↔ ancien)
- **Montant** ↕️ : Numérique (petit ↔ grand)
- **Type** ↕️ : Alphabétique (Dépenses ↔ Revenus)
- **Catégorie** ↕️ : Alphabétique (A ↔ Z)
- **Note** ↕️ : Alphabétique (A ↔ Z)
- **Pointage** ↕️ : Statut (Non pointées ↔ Pointées)

#### Indicateurs visuels
- **↕️** : Colonne non triée (cliquez pour trier)
- **↑** : Tri croissant (cliquez pour inverser)
- **↓** : Tri décroissant (cliquez pour inverser)

### 🔍 Filtres et recherche avancés

#### Accès aux filtres
Cliquez sur **"▶️ Filtres et recherche"** pour ouvrir le panneau de filtrage.

#### Types de filtres disponibles

**🔍 Recherche textuelle**
- Recherche dans les descriptions ET les noms de catégories
- Insensible à la casse
- Résultats en temps réel

**💰 Filtre par type**
- Tous / Revenus / Dépenses

**🏷️ Filtre par catégorie**
- Liste déroulante avec toutes vos catégories
- Tri alphabétique automatique

**✅ Filtre par statut de pointage**
- Toutes / Pointées / Non pointées

**📅 Filtres par période**
- Date de début (de)
- Date de fin (à)
- Sélecteurs de date intuitifs

**💶 Filtres par montant**
- Montant minimum
- Montant maximum
- Support des décimales (ex: 12.50)

#### Fonctionnalités utiles
- **Compteur de résultats** : Affiche le nombre de transactions correspondantes
- **Bouton Reset** 🔄 : Remet tous les filtres et le tri à zéro
- **Astuces intégrées** : Conseils d'utilisation dans l'interface

### 🔮 Transactions futures
- **Case à cocher** : "Afficher les transactions futures" (si transactions futures existent)
- **Style distinctif** : Fond bleu clair, texte italique, icône 🔮
- **Compteur** : Nombre de transactions futures entre parenthèses

### Actions sur les transactions
- **Modifier** ✏️ : Cliquez pour éditer (sauf si pointée)
- **Supprimer** 🗑️ : Cliquez puis confirmez (sauf si pointée)
- **Protection** : Les transactions pointées ne peuvent plus être modifiées/supprimées

### Exemples d'utilisation

#### Recherche rapide
- **Trouver un restaurant** : Tapez le nom dans la recherche
- **Voir les dépenses alimentaires** : Filtre catégorie "🍽️ Alimentation"
- **Transactions du mois dernier** : Définir les dates de début/fin

#### Analyse financière
- **Gros montants** : Filtre montant minimum élevé
- **Transactions non pointées** : Filtre statut "Non pointées"
- **Revenus par ordre croissant** : Filtre type "Revenus" + tri montant ↑

## ✅ Pointage des transactions

### Accès
Cliquez sur "✅ Pointage" dans le menu.

### Principe du pointage
Le pointage permet de **valider vos transactions avec votre relevé bancaire**. Une fois pointée, une transaction devient **immutable** (ne peut plus être modifiée ni supprimée).

### Interface de pointage

#### Filtres de pointage
- **Date de début/fin** : Par défaut sur la date du jour
- **Montant min/max** : Par défaut de 0 à 10000€
- **Type** : Par défaut sur "Dépenses"
- **Tous les filtres** de l'historique sont disponibles

#### Liste des transactions à pointer
- **Seules les transactions passées/présentes** apparaissent
- **Les transactions futures** sont automatiquement exclues
- **Sélection individuelle** : Case à cocher par transaction
- **Sélection multiple** : Case "Tout sélectionner"

### Processus de pointage

#### Étape 1 : Filtrer
1. **Ajustez les filtres** selon votre relevé bancaire
2. **Définissez la période** correspondant à votre relevé
3. **Sélectionnez le type** (dépenses/revenus) si nécessaire

#### Étape 2 : Sélectionner
1. **Cochez les transactions** qui apparaissent sur votre relevé
2. **Utilisez "Tout sélectionner"** pour pointer en lot
3. **Vérifiez les montants** et dates avec votre relevé

#### Étape 3 : Valider
1. **Cliquez sur "Pointer les transactions sélectionnées"**
2. **Confirmez** l'action (irréversible)
3. **Les transactions pointées** disparaissent de la liste

### Après pointage
- **Dans l'historique** : Badge ✅ "Pointée" avec date de pointage
- **Protection** : Boutons Modifier/Supprimer désactivés
- **Traçabilité** : Date et heure de pointage enregistrées

### Bonnes pratiques
1. **Pointez régulièrement** (chaque relevé bancaire)
2. **Vérifiez attentivement** avant de pointer
3. **Utilisez les filtres** pour isoler les transactions du relevé
4. **Ne pointez que les transactions confirmées** par la banque

## 💡 Conseils d'utilisation

### Bonnes pratiques

#### Saisie des transactions
1. **Saisissez régulièrement** vos transactions
2. **Utilisez des descriptions claires** pour vous souvenir
3. **Profitez du champ date** pour saisir des transactions passées ou futures
4. **Anticipez** : Saisissez les prélèvements futurs pour prévoir votre budget

#### Gestion des catégories
5. **Utilisez le sélecteur d'emoji** pour des catégories visuelles
6. **Créez des catégories spécifiques** pour un meilleur suivi
7. **Organisez par thèmes** : Maison, Transport, Loisirs, etc.

#### Suivi et analyse
8. **Consultez le dashboard** pour suivre votre budget
9. **Utilisez les filtres** de l'historique pour analyser vos dépenses
10. **Pointez régulièrement** vos transactions avec vos relevés
11. **Exploitez le tri** pour identifier les tendances

### Catégories recommandées
- **Fixes** : Loyer, Assurances, Abonnements
- **Variables** : Alimentation, Loisirs, Vêtements
- **Exceptionnelles** : Réparations, Cadeaux, Voyages
- **Revenus** : Salaire, Primes, Autres revenus

### Astuces d'interface

#### Navigation efficace
- **Cliquez sur les en-têtes** de colonnes pour trier rapidement
- **Utilisez la recherche** pour trouver une transaction spécifique
- **Exploitez les filtres** pour des analyses ciblées
- **Activez "Afficher les transactions futures"** pour voir votre planning

#### Raccourcis utiles
- **Bouton Reset** 🔄 : Remet tous les filtres à zéro
- **Sélecteur d'emoji** 😀 : Accès rapide à 500+ emojis organisés
- **Filtres pliables** : Masquez/affichez selon vos besoins
- **Compteurs en temps réel** : Suivez le nombre de résultats

### Organisation avancée
- **Séparez transactions passées/futures** : Utilisez la case à cocher appropriée
- **Créez des catégories thématiques** avec le sélecteur d'emoji
- **Pointez par période** : Suivez vos relevés bancaires
- **Analysez par tri** : Identifiez vos plus grosses dépenses

## 🔒 Sécurité

### Déconnexion
- Cliquez sur "🚪 Déconnexion" en haut à droite
- Vos données restent sauvegardées

### Données
- Toutes vos données sont stockées localement
- Aucune information n'est envoyée sur internet
- Base de données SQLite sécurisée

## 🆕 Nouvelles fonctionnalités

### Transactions futures
- **Planification** : Saisissez vos prélèvements et revenus futurs
- **Séparation claire** : Section dédiée dans le dashboard
- **Protection** : Les transactions futures ne peuvent pas être pointées
- **Visibilité** : Option d'affichage dans l'historique avec style distinctif

### Sélecteur d'emoji avancé
- **500+ emojis** organisés en 13 catégories thématiques
- **Interface intuitive** avec grille et fermeture automatique
- **Intégration intelligente** : Remplacement ou ajout automatique
- **Catégories visuelles** : Rendez vos finances plus attrayantes

### Système de tri et filtrage
- **Tri par colonnes** : Cliquez sur n'importe quel en-tête
- **Filtres multiples** : Date, montant, catégorie, type, pointage
- **Recherche textuelle** : Dans descriptions et catégories
- **Interface guidée** : Compteurs, reset, astuces intégrées

### Pointage bancaire
- **Validation sécurisée** : Rapprochement avec vos relevés
- **Protection des données** : Transactions pointées immutables
- **Filtrage intelligent** : Exclusion automatique des transactions futures
- **Traçabilité complète** : Date et heure de pointage

## 🐛 Problèmes courants

### "Erreur de connexion"
- Vérifiez que le serveur est démarré
- Actualisez la page
- Consultez les logs avec `tail -f server.log`

### "Catégorie non trouvée"
- Assurez-vous d'avoir des catégories créées
- Utilisez le bouton "Ajouter catégories par défaut"
- Essayez le sélecteur d'emoji pour créer rapidement

### "Erreur lors du chargement des transactions" (Pointage)
- Vérifiez que vous avez des transactions non pointées
- Les transactions futures n'apparaissent pas dans le pointage
- Actualisez la page si le problème persiste

### Interface qui ne répond pas
- Actualisez la page (F5)
- Vérifiez la console développeur (F12)
- Redémarrez avec `./restart-all.sh`

### Filtres qui ne fonctionnent pas
- Utilisez le bouton "Reset" 🔄 pour remettre à zéro
- Vérifiez que vos critères ne sont pas trop restrictifs
- Les transactions futures nécessitent d'activer l'option dédiée

## 📱 Utilisation mobile

L'application est entièrement optimisée pour mobile :
- **Navigation tactile** intuitive avec gestes
- **Menu adaptatif** avec icônes et labels
- **Formulaires** optimisés pour le tactile
- **Sélecteur d'emoji** adapté aux écrans tactiles
- **Filtres responsive** qui s'adaptent à la taille d'écran
- **Tableaux scrollables** horizontalement sur petits écrans
- **Boutons de tri** facilement cliquables au doigt

### Spécificités mobiles
- **Sélection multiple** : Cases à cocher adaptées au tactile
- **Filtres pliables** : Économisent l'espace d'écran
- **Messages d'aide** : Tooltips adaptés au mobile
- **Performance** : Filtrage en temps réel même sur mobile

## 🎯 Résumé des fonctionnalités

### Gestion complète
✅ **Transactions** : Ajout, modification, suppression avec dates futures
✅ **Catégories** : Création avec sélecteur d'emoji (500+ icônes)
✅ **Pointage** : Rapprochement bancaire sécurisé
✅ **Historique** : Tri et filtrage avancés

### Interface moderne
✅ **Dashboard** : Vue d'ensemble avec transactions futures séparées
✅ **Navigation** : Menu intuitif avec 4 sections principales
✅ **Responsive** : Optimisé mobile, tablette et desktop
✅ **Accessibilité** : Tooltips, messages d'aide, compteurs

### Fonctionnalités avancées
✅ **Tri multi-colonnes** : Cliquez sur n'importe quel en-tête
✅ **Filtres multiples** : Date, montant, catégorie, type, statut
✅ **Recherche intelligente** : Dans descriptions et catégories
✅ **Transactions futures** : Planification et séparation visuelle

---

**💡 Besoin d'aide ?** Consultez le [guide développeur](DEVELOPER_GUIDE.md) ou les [scripts de gestion](../SCRIPTS.md).

**🚀 Version actuelle** : Application complète avec gestion des transactions futures, sélecteur d'emoji avancé, tri/filtrage intelligent et pointage bancaire sécurisé.
