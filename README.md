# 💰 Binou Compta Perso

Une application web moderne et intuitive pour gérer sa comptabilité personnelle, développée avec React et Node.js.

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)
![Node](https://img.shields.io/badge/node-%3E%3D16.0.0-brightgreen.svg)

## ✨ Fonctionnalités

### 🔐 **Authentification sécurisée**
- Système de login/mot de passe
- Gestion des sessions avec tokens
- Protection de toutes les routes API

### 📊 **Dashboard intelligent**
- Vue d'ensemble des finances
- Statistiques en temps réel
- Transactions récentes
- Calcul automatique du solde

### 💸 **Gestion des transactions**
- Ajout rapide de revenus et dépenses
- Catégorisation automatique
- Historique complet avec filtres
- Suppression sécurisée

### 🏷️ **Catégories personnalisables**
- 20 catégories pré-configurées
- Création de catégories personnalisées
- Gestion complète (ajout/suppression)
- Interface intuitive avec émojis

### 📱 **Design moderne**
- Interface responsive (mobile-friendly)
- Thème moderne avec gradients
- Animations fluides
- Notifications toast

## 🚀 Démarrage rapide

### Prérequis
- Node.js >= 16.0.0
- npm >= 8.0.0

### Installation

```bash
# Cloner le projet
git clone <votre-repo>
cd MaComptaPerso

# Installer les dépendances du serveur
cd server
npm install

# Installer les dépendances du client
cd ../client
npm install
cd ..
```

### Lancement

```bash
# Méthode 1 : Script principal (recommandé)
./macompta.sh dev          # Mode développement
./macompta.sh start        # Mode production

# Méthode 2 : Scripts directs
./dev.sh                   # Mode développement
./start.sh                 # Mode production

# Méthode 3 : Scripts dans bin/
./bin/dev                  # Mode développement
./bin/start                # Mode production

# Méthode 4 : Lancement manuel
cd server && npm start     # Serveur
cd client && npm run dev   # Client (développement)
```

### Arrêt

```bash
./macompta.sh stop         # Script principal
./stop.sh                  # Script direct
./bin/stop                 # Script dans bin/
```

### Accès à l'application

- **Application web** : http://localhost:5173
- **API Backend** : http://localhost:3333

### Connexion par défaut

- **Utilisateur** : `admin`
- **Mot de passe** : `password`

## 📖 Documentation

- [📋 Guide d'utilisation](docs/USER_GUIDE.md)
- [🔧 Guide développeur](docs/DEVELOPER_GUIDE.md)
- [🚀 Scripts de gestion](SCRIPTS.md)
- [🏗️ Architecture](docs/ARCHITECTURE.md)

## 🛠️ Technologies utilisées

### Frontend
- **React 18** - Interface utilisateur
- **Vite** - Build tool moderne
- **CSS3** - Styles avec variables CSS

### Backend
- **Node.js** - Runtime JavaScript
- **Express** - Framework web
- **SQLite3** - Base de données
- **CORS** - Gestion des requêtes cross-origin

## 📁 Structure du projet

```
MaComptaPerso/
├── client/                 # Application React
│   ├── src/
│   │   ├── components/     # Composants React
│   │   ├── styles.css      # Styles globaux
│   │   └── main.jsx        # Point d'entrée
│   └── package.json
├── server/                 # API Node.js
│   ├── data/              # Base de données SQLite
│   ├── db.js              # Configuration DB
│   ├── index.js           # Serveur Express
│   └── package.json
├── docs/                  # Documentation
├── *.sh                   # Scripts de gestion
└── README.md
```

## 🎯 Utilisation

1. **Connexion** avec les identifiants par défaut
2. **Dashboard** - Vue d'ensemble de vos finances
3. **Ajouter Transaction** - Enregistrer revenus/dépenses
4. **Catégories** - Gérer vos catégories personnalisées
5. **Historique** - Consulter toutes vos transactions

## 🔧 Scripts disponibles

```bash
./restart-all.sh      # Redémarrer serveur + client
./dev.sh              # Mode développement
./stop-all.sh         # Arrêter tous les processus
```

## 🤝 Contribution

Les contributions sont les bienvenues ! Consultez le [guide développeur](docs/DEVELOPER_GUIDE.md).

## 📄 License

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 👨‍💻 Auteur

Développé avec ❤️ pour une gestion financière personnelle simplifiée.

---

**🌟 N'hésitez pas à laisser une étoile si ce projet vous aide !**
