#!/bin/bash
# macompta.sh - Script principal de gestion de Ma Compta Perso

set -e

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

show_help() {
    echo "🚀 Ma Compta Perso - Script de gestion principal"
    echo "================================================"
    echo ""
    echo "Usage: $0 <commande> [options]"
    echo ""
    echo "📋 Commandes de fonctionnement :"
    echo "  dev                 Démarrer en mode développement"
    echo "  start               Démarrer en mode production"
    echo "  stop                Arrêter l'application"
    echo "  restart             Redémarrer l'application"
    echo "  restart-server      Redémarrer uniquement le serveur"
    echo "  restart-client      Redémarrer uniquement le client"
    echo ""
    echo "🛠️  Commandes de développement :"
    echo "  release [type]      Créer une release (patch|minor|major)"
    echo "  changelog           Générer le changelog"
    echo "  sync-versions       Synchroniser les versions"
    echo ""
    echo "🚀 Commandes de déploiement :"
    echo "  install             Installation automatique"
    echo "  start-prod          Démarrer en production (optimisé)"
    echo "  stop-prod           Arrêter la production"
    echo ""
    echo "📊 Commandes d'information :"
    echo "  status              Afficher le statut de l'application"
    echo "  logs                Afficher les logs en temps réel"
    echo "  help                Afficher cette aide"
    echo ""
    echo "📚 Documentation :"
    echo "  - SCRIPTS.md                 Guide des scripts"
    echo "  - docs/IMPLEMENTATION.md     Guide de déploiement"
    echo "  - bin/README.md              Scripts quotidiens"
    echo "  - dev-tools/README.md        Outils de développement"
    echo "  - deployment/README.md       Scripts de déploiement"
}

show_status() {
    echo "📊 Statut de Ma Compta Perso"
    echo "============================"
    
    # Vérifier les processus
    SERVER_PID=$(pgrep -f "node.*index.js" 2>/dev/null || echo "")
    CLIENT_PID=$(pgrep -f "vite.*preview\|vite.*dev" 2>/dev/null || echo "")
    
    if [ -n "$SERVER_PID" ]; then
        log_success "Serveur backend actif (PID: $SERVER_PID)"
    else
        log_warning "Serveur backend arrêté"
    fi
    
    if [ -n "$CLIENT_PID" ]; then
        log_success "Client React actif (PID: $CLIENT_PID)"
    else
        log_warning "Client React arrêté"
    fi
    
    # Vérifier les ports
    if netstat -tlnp 2>/dev/null | grep -q ":3333"; then
        log_success "Port 3333 (API) ouvert"
    else
        log_warning "Port 3333 (API) fermé"
    fi
    
    if netstat -tlnp 2>/dev/null | grep -q ":5173"; then
        log_success "Port 5173 (Web) ouvert"
    else
        log_warning "Port 5173 (Web) fermé"
    fi
    
    echo ""
    echo "🌐 URLs :"
    echo "  Application : http://localhost:5173"
    echo "  API Backend : http://localhost:3333"
    echo ""
    echo "📊 Logs disponibles :"
    if [ -f "production.log" ]; then
        echo "  production.log ($(wc -l < production.log) lignes)"
    fi
    if [ -f "server.log" ]; then
        echo "  server.log ($(wc -l < server.log) lignes)"
    fi
    if [ -f "client.log" ]; then
        echo "  client.log ($(wc -l < client.log) lignes)"
    fi
}

show_logs() {
    echo "📊 Logs en temps réel - Ma Compta Perso"
    echo "======================================="
    echo "Appuyez sur Ctrl+C pour arrêter"
    echo ""
    
    # Trouver le fichier de log le plus récent
    if [ -f "production.log" ]; then
        tail -f production.log
    elif [ -f "server.log" ]; then
        tail -f server.log
    elif [ -f "server-dev.log" ]; then
        tail -f server-dev.log
    else
        log_warning "Aucun fichier de log trouvé"
        echo "Démarrez l'application d'abord avec : $0 dev ou $0 start"
    fi
}

# Fonction principale
main() {
    case "${1:-help}" in
        "dev")
            log_info "Démarrage en mode développement..."
            exec ./bin/dev "${@:2}"
            ;;
        "start")
            log_info "Démarrage en mode production..."
            exec ./bin/start "${@:2}"
            ;;
        "stop")
            log_info "Arrêt de l'application..."
            exec ./bin/stop "${@:2}"
            ;;
        "restart")
            log_info "Redémarrage de l'application..."
            ./bin/stop
            sleep 2
            exec ./bin/start "${@:2}"
            ;;
        "restart-server")
            log_info "Redémarrage du serveur..."
            exec ./bin/restart-server "${@:2}"
            ;;
        "restart-client")
            log_info "Redémarrage du client..."
            exec ./bin/restart-client "${@:2}"
            ;;
        "release")
            TYPE=${2:-patch}
            log_info "Création d'une release $TYPE..."
            exec ./dev-tools/release.sh "$TYPE"
            ;;
        "changelog")
            log_info "Génération du changelog..."
            exec node dev-tools/generate-changelog.js
            ;;
        "sync-versions")
            log_info "Synchronisation des versions..."
            exec node dev-tools/update-versions.js
            ;;
        "install")
            log_info "Installation automatique..."
            exec ./deployment/install-macompta.sh "${@:2}"
            ;;
        "start-prod")
            log_info "Démarrage production optimisé..."
            exec ./deployment/start-production.sh "${@:2}"
            ;;
        "stop-prod")
            log_info "Arrêt production..."
            exec ./deployment/stop-production.sh "${@:2}"
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Commande inconnue: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

main "$@"
