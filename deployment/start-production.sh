#!/bin/bash
# start-production.sh - Script de démarrage en mode production

set -e

APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$APP_DIR"

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo "🚀 Démarrage de Ma Compta Perso en mode production"
echo "================================================="

# Vérifier les prérequis
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé. Installez-le d'abord."
    exit 1
fi

# Vérifier que les dépendances sont installées
if [ ! -d "node_modules" ] || [ ! -d "client/node_modules" ] || [ ! -d "server/node_modules" ]; then
    log_info "Installation des dépendances..."
    npm run install:all
fi

# Construire le client si nécessaire
if [ ! -d "client/dist" ] || [ "client/src" -nt "client/dist" ]; then
    log_info "Construction du client pour la production..."
    cd client && npm run build && cd ..
fi

# Arrêter les processus existants
log_info "Arrêt des processus existants..."
pkill -f "node.*index.js" 2>/dev/null || true
pkill -f "vite.*preview" 2>/dev/null || true
sleep 2

# Créer les répertoires nécessaires
mkdir -p server/data server/backups

# Créer le fichier .env s'il n'existe pas
if [ ! -f "server/.env" ]; then
    log_info "Création du fichier de configuration..."
    cat > "server/.env" << 'EOF'
PORT=3333
NODE_ENV=production
DB_PATH=./data/database.sqlite
EOF
fi

# Démarrer le serveur backend
log_info "Démarrage du serveur backend..."
cd server
node index.js > ../production.log 2>&1 &
SERVER_PID=$!
cd ..

# Attendre que le serveur démarre
sleep 3

# Vérifier que le serveur est démarré
if ! curl -s http://localhost:3333 > /dev/null 2>&1; then
    echo "❌ Le serveur backend n'a pas pu démarrer. Vérifiez les logs :"
    tail -20 production.log
    exit 1
fi

# Démarrer le serveur de fichiers statiques
log_info "Démarrage du serveur de fichiers statiques..."
cd client
npx vite preview --port 5173 --host >> ../production.log 2>&1 &
CLIENT_PID=$!
cd ..

# Sauvegarder les PIDs
echo $SERVER_PID > server.pid
echo $CLIENT_PID > client.pid

# Attendre que le client démarre
sleep 3

log_success "Application démarrée avec succès !"
echo "=================================="
echo "🌐 Application web : http://localhost:5173"
echo "🔧 API Backend    : http://localhost:3333"
echo "📊 Logs          : tail -f production.log"
echo "🛑 Pour arrêter  : ./stop-production.sh"
echo ""
echo "👤 Connexion par défaut :"
echo "   Utilisateur : admin"
echo "   Mot de passe : password"
echo ""

# Ouvrir le navigateur automatiquement
sleep 2
if command -v xdg-open &> /dev/null; then
    log_info "Ouverture du navigateur..."
    xdg-open http://localhost:5173 &
elif command -v gnome-open &> /dev/null; then
    gnome-open http://localhost:5173 &
elif command -v firefox &> /dev/null; then
    firefox http://localhost:5173 &
fi

log_warning "L'application fonctionne en arrière-plan."
log_warning "Utilisez './stop-production.sh' pour l'arrêter."
