#!/bin/bash
# stop-production.sh - Script d'arrêt en mode production

APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$APP_DIR"

# Couleurs pour les messages
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🛑 Arrêt de <PERSON>mpt<PERSON>"
echo "============================"

# Arrêter via les PIDs sauvegardés
if [ -f "server.pid" ]; then
    SERVER_PID=$(cat server.pid)
    log_info "Arrêt du serveur backend (PID: $SERVER_PID)..."
    if kill $SERVER_PID 2>/dev/null; then
        log_success "Serveur backend arrêté"
    else
        log_error "Impossible d'arrêter le serveur backend via PID"
    fi
    rm server.pid
fi

if [ -f "client.pid" ]; then
    CLIENT_PID=$(cat client.pid)
    log_info "Arrêt du serveur client (PID: $CLIENT_PID)..."
    if kill $CLIENT_PID 2>/dev/null; then
        log_success "Serveur client arrêté"
    else
        log_error "Impossible d'arrêter le serveur client via PID"
    fi
    rm client.pid
fi

# Arrêt forcé si nécessaire
log_info "Vérification des processus restants..."
REMAINING_PROCESSES=$(pgrep -f "node.*index.js" 2>/dev/null || true)
if [ -n "$REMAINING_PROCESSES" ]; then
    log_info "Arrêt forcé des processus serveur restants..."
    pkill -f "node.*index.js" 2>/dev/null || true
fi

REMAINING_VITE=$(pgrep -f "vite.*preview" 2>/dev/null || true)
if [ -n "$REMAINING_VITE" ]; then
    log_info "Arrêt forcé des processus Vite restants..."
    pkill -f "vite.*preview" 2>/dev/null || true
fi

# Attendre un peu pour que les processus se terminent proprement
sleep 2

# Vérification finale
if pgrep -f "node.*index.js" > /dev/null 2>&1 || pgrep -f "vite.*preview" > /dev/null 2>&1; then
    log_error "Certains processus sont encore actifs. Arrêt forcé..."
    pkill -9 -f "node.*index.js" 2>/dev/null || true
    pkill -9 -f "vite.*preview" 2>/dev/null || true
fi

log_success "Application arrêtée avec succès !"
echo ""
echo "📊 Les logs sont conservés dans : production.log"
echo "🚀 Pour redémarrer : ./start-production.sh"
