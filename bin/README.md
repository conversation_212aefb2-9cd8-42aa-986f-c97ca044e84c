# 🔧 Scripts de fonctionnement - bin/

Ce dossier contient les scripts de fonctionnement quotidien de l'application.

## 📋 Scripts disponibles

### 🚀 `dev` - Mode développement
Lance l'application en mode développement avec rechargement automatique.
```bash
./bin/dev
```
- D<PERSON><PERSON>re le serveur backend avec nodemon
- Démarre le client React avec Vite en mode dev
- Rechargement automatique des modifications

### ▶️ `start` - Démarrage production
Lance l'application en mode production.
```bash
./bin/start
```
- Démarre le serveur backend
- Démarre le client React avec Vite preview
- Ouvre automatiquement le navigateur

### ⏹️ `stop` - Arrêt de l'application
Arrête tous les processus de l'application.
```bash
./bin/stop
```
- Arrête le serveur backend
- Arrête le client React
- Nettoie les fichiers PID

### 🔄 `restart-client` - Redémarrage client
Redémarre uniquement le client React.
```bash
./bin/restart-client
```

### 🔄 `restart-server` - Redémarrage serveur
Redémarre uniquement le serveur backend.
```bash
./bin/restart-server
```

## 🎯 Utilisation

Ces scripts sont conçus pour être utilisés depuis la racine du projet :

```bash
# Depuis la racine du projet
./bin/dev      # Mode développement
./bin/start    # Mode production
./bin/stop     # Arrêt

# Ou via les raccourcis à la racine
./dev.sh       # Équivalent à ./bin/dev
./start.sh     # Équivalent à ./bin/start
./stop.sh      # Équivalent à ./bin/stop
```

## 📊 Logs

Les logs sont générés dans la racine du projet :
- `server.log` - Logs du serveur backend
- `client.log` - Logs du client React
- `server-dev.log` - Logs du serveur en mode dev
- `client-dev.log` - Logs du client en mode dev

## 🔍 Dépannage

Si un script ne fonctionne pas :
1. Vérifiez les permissions : `chmod +x bin/*`
2. Vérifiez les logs correspondants
3. Utilisez `./bin/stop` puis relancez
