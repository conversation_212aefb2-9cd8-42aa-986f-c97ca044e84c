#!/bin/bash

echo "🚀 Mode développement - <PERSON><PERSON>"
echo "=========================================="
echo "Ce script démarre l'application en mode développement"
echo "avec rechargement automatique des modifications."
echo ""

# Vérifier si nodemon est installé
if ! command -v nodemon &> /dev/null; then
    echo "📦 Installation de nodemon pour le rechargement automatique..."
    npm install -g nodemon
fi

# Arrêter les processus existants
echo "🛑 Arrêt des processus existants..."
pkill -f "node index.js" 2>/dev/null
pkill -f "vite" 2>/dev/null
pkill -f "nodemon" 2>/dev/null
sleep 2

echo ""
echo "🚀 Démarrage en mode développement..."
echo "===================================="

# Démarrer le serveur avec nodemon (rechargement auto)
echo "🔧 Serveur backend avec rechargement automatique..."
cd server
nodemon index.js > ../server-dev.log 2>&1 &
SERVER_PID=$!
cd ..

sleep 3

# Démarrer le client Vite (rechargement auto intégré)
echo "⚛️  Client React avec rechargement automatique..."
cd client
npm run dev > ../client-dev.log 2>&1 &
CLIENT_PID=$!
cd ..

sleep 5

echo ""
echo "✅ Mode développement actif !"
echo "============================="
echo "🌐 Application : http://localhost:5173"
echo "🔧 API Backend : http://localhost:3333"
echo ""
echo "🔄 Rechargement automatique activé :"
echo "   - Serveur : modifications des fichiers .js"
echo "   - Client  : modifications des fichiers React"
echo ""
echo "📊 Logs en temps réel :"
echo "   - Serveur : tail -f server-dev.log"
echo "   - Client  : tail -f client-dev.log"
echo ""
echo "🛑 Pour arrêter : Ctrl+C ou ./stop-all.sh"

# Sauvegarder les PIDs
echo $SERVER_PID > server.pid
echo $CLIENT_PID > client.pid

# Attendre l'interruption
trap 'echo ""; echo "🛑 Arrêt du mode développement..."; kill $SERVER_PID $CLIENT_PID 2>/dev/null; rm -f server.pid client.pid; exit' INT

# Garder le script actif
wait
