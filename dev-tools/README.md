# 🛠️ Outils de développement - dev-tools/

Ce dossier contient les outils et scripts utilisés pour le développement et la maintenance du projet.

## 📋 Scripts disponibles

### 🚀 `release.sh` - Gestion des releases
Script automatisé pour créer des releases du projet.
```bash
./dev-tools/release.sh [patch|minor|major]
```

**Fonctionnalités :**
- Incrémente automatiquement la version
- Met à jour tous les package.json
- Génère le changelog automatiquement
- Crée les tags Git
- Construit l'application
- Propose le push automatique

**Exemples :**
```bash
./dev-tools/release.sh patch    # 1.0.0 → 1.0.1
./dev-tools/release.sh minor    # 1.0.1 → 1.1.0
./dev-tools/release.sh major    # 1.1.0 → 2.0.0
```

### 📝 `generate-changelog.js` - Génération du changelog
Génère automatiquement le changelog basé sur les commits Git.
```bash
node dev-tools/generate-changelog.js
```

**Fonctionnalités :**
- <PERSON>ly<PERSON> les commits depuis le dernier tag
- Catégorise les changements (feat, fix, docs, etc.)
- Génère un changelog formaté en Markdown
- Met à jour le fichier CHANGELOG.md

### 🔄 `update-versions.js` - Synchronisation des versions
Synchronise les versions entre tous les package.json du projet.
```bash
node dev-tools/update-versions.js
```

**Fonctionnalités :**
- Lit la version du package.json racine
- Met à jour client/package.json
- Met à jour server/package.json
- Assure la cohérence des versions

## 🎯 Workflow de développement

### Créer une nouvelle release

1. **Développement terminé** - Tous les changements sont commitées
2. **Tests passés** - L'application fonctionne correctement
3. **Lancer la release** :
   ```bash
   ./dev-tools/release.sh patch  # ou minor/major
   ```
4. **Vérifier** - Le script fait tout automatiquement :
   - ✅ Incrémente la version
   - ✅ Met à jour les package.json
   - ✅ Génère le changelog
   - ✅ Construit l'application
   - ✅ Crée le commit et le tag
   - ✅ Propose le push

### Générer manuellement le changelog

```bash
# Après avoir ajouté des commits
node dev-tools/generate-changelog.js
```

### Synchroniser les versions

```bash
# Si les versions sont désynchronisées
node dev-tools/update-versions.js
```

## 📊 Format des commits

Pour un changelog optimal, utilisez le format conventionnel :

```
feat: nouvelle fonctionnalité
fix: correction de bug
docs: mise à jour documentation
style: formatage, point-virgules manquants, etc.
refactor: refactoring du code
test: ajout de tests
chore: maintenance, dépendances, etc.
```

## 🔧 Configuration

### Variables d'environnement

Les scripts utilisent les variables Git standard :
- `GIT_AUTHOR_NAME`
- `GIT_AUTHOR_EMAIL`

### Prérequis

- Node.js >= 16
- Git configuré
- Droits de push sur le repository

## 🚨 Attention

⚠️ **Ces scripts modifient le code et l'historique Git !**

- Toujours travailler sur une branche propre
- Vérifier que tous les tests passent avant une release
- Ne pas interrompre le processus de release
- Faire un backup avant les opérations importantes
