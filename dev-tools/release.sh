#!/bin/bash

# Script de release pour Ma Compta Perso
# Usage: ./release.sh [patch|minor|major]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier que nous sommes sur la branche main/master
CURRENT_BRANCH=$(git branch --show-current)
if [[ "$CURRENT_BRANCH" != "main" && "$CURRENT_BRANCH" != "master" ]]; then
    log_error "Vous devez être sur la branche main ou master pour faire une release"
    exit 1
fi

# Vérifier qu'il n'y a pas de changements non commitées
if [[ -n $(git status --porcelain) ]]; then
    log_error "Il y a des changements non commitées. Veuillez les committer avant de faire une release."
    exit 1
fi

# Type de version (patch par défaut)
VERSION_TYPE=${1:-patch}

if [[ "$VERSION_TYPE" != "patch" && "$VERSION_TYPE" != "minor" && "$VERSION_TYPE" != "major" ]]; then
    log_error "Type de version invalide. Utilisez: patch, minor, ou major"
    exit 1
fi

log_info "Début de la release $VERSION_TYPE..."

# Installer les dépendances si nécessaire
if [[ ! -d "node_modules" ]]; then
    log_info "Installation des dépendances..."
    npm install
fi

# Mettre à jour la version
log_info "Mise à jour de la version ($VERSION_TYPE)..."
npm run version:$VERSION_TYPE

# Obtenir la nouvelle version
NEW_VERSION=$(node -p "require('./package.json').version")
log_success "Nouvelle version: $NEW_VERSION"

# Construire l'application
log_info "Construction de l'application..."
npm run build

# Ajouter tous les fichiers modifiés
git add .

# Committer les changements
log_info "Commit des changements..."
git commit -m "chore: release v$NEW_VERSION" || log_warning "Rien à committer"

# Créer le tag
log_info "Création du tag v$NEW_VERSION..."
git tag "v$NEW_VERSION"

# Demander confirmation pour push
echo
log_warning "Prêt à pusher la release v$NEW_VERSION"
read -p "Voulez-vous continuer ? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "Push des changements et du tag..."
    git push origin $CURRENT_BRANCH
    git push origin "v$NEW_VERSION"
    
    log_success "Release v$NEW_VERSION créée avec succès ! 🎉"
    log_info "GitHub Actions va maintenant créer la release automatiquement."
else
    log_warning "Release annulée. Pour pusher plus tard:"
    echo "  git push origin $CURRENT_BRANCH"
    echo "  git push origin v$NEW_VERSION"
fi
