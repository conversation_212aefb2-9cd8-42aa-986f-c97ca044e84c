#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

const version = JSON.parse(fs.readFileSync('package.json', 'utf8')).version;
const date = new Date().toISOString().split('T')[0];

console.log(`📝 Génération du changelog pour la version ${version}...`);

try {
  // Obtenir les commits depuis le dernier tag
  let commits;
  try {
    const lastTag = execSync('git describe --tags --abbrev=0 HEAD~1', { encoding: 'utf8' }).trim();
    commits = execSync(`git log ${lastTag}..HEAD --oneline --no-merges`, { encoding: 'utf8' });
  } catch (e) {
    // Si pas de tag précédent, prendre tous les commits
    commits = execSync('git log --oneline --no-merges', { encoding: 'utf8' });
  }

  if (!commits.trim()) {
    console.log('⚠️  Aucun nouveau commit trouvé');
    return;
  }

  // Parser les commits
  const commitLines = commits.trim().split('\n');
  const features = [];
  const fixes = [];
  const others = [];

  commitLines.forEach(line => {
    const [hash, ...messageParts] = line.split(' ');
    const message = messageParts.join(' ');
    
    if (message.toLowerCase().includes('feat:') || message.toLowerCase().includes('feature:')) {
      features.push(`- ${message.replace(/^(feat:|feature:)\s*/i, '')}`);
    } else if (message.toLowerCase().includes('fix:') || message.toLowerCase().includes('bug:')) {
      fixes.push(`- ${message.replace(/^(fix:|bug:)\s*/i, '')}`);
    } else {
      others.push(`- ${message}`);
    }
  });

  // Générer le contenu du changelog
  let changelogEntry = `## [${version}] - ${date}\n\n`;
  
  if (features.length > 0) {
    changelogEntry += `### ✨ Nouvelles fonctionnalités\n${features.join('\n')}\n\n`;
  }
  
  if (fixes.length > 0) {
    changelogEntry += `### 🐛 Corrections de bugs\n${fixes.join('\n')}\n\n`;
  }
  
  if (others.length > 0) {
    changelogEntry += `### 🔧 Autres changements\n${others.join('\n')}\n\n`;
  }

  // Lire le changelog existant ou créer un nouveau
  let existingChangelog = '';
  if (fs.existsSync('CHANGELOG.md')) {
    existingChangelog = fs.readFileSync('CHANGELOG.md', 'utf8');
  } else {
    existingChangelog = '# Changelog\n\nTous les changements notables de ce projet seront documentés dans ce fichier.\n\n';
  }

  // Insérer la nouvelle entrée au début (après le header)
  const lines = existingChangelog.split('\n');
  const headerEndIndex = lines.findIndex(line => line.startsWith('## '));
  
  let newChangelog;
  if (headerEndIndex === -1) {
    // Pas d'entrée existante, ajouter après le header
    newChangelog = lines.slice(0, 3).join('\n') + '\n' + changelogEntry + lines.slice(3).join('\n');
  } else {
    // Insérer avant la première entrée existante
    newChangelog = lines.slice(0, headerEndIndex).join('\n') + '\n' + changelogEntry + lines.slice(headerEndIndex).join('\n');
  }

  fs.writeFileSync('CHANGELOG.md', newChangelog);
  console.log(`✅ Changelog mis à jour avec ${commitLines.length} commits`);

} catch (error) {
  console.error('❌ Erreur lors de la génération du changelog:', error.message);
}
