#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Lire la version du package.json racine
const rootPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const version = rootPackage.version;

console.log(`🔄 Mise à jour des versions vers ${version}...`);

// Mettre à jour client/package.json
const clientPackagePath = path.join('client', 'package.json');
if (fs.existsSync(clientPackagePath)) {
  const clientPackage = JSON.parse(fs.readFileSync(clientPackagePath, 'utf8'));
  clientPackage.version = version;
  fs.writeFileSync(clientPackagePath, JSON.stringify(clientPackage, null, 2) + '\n');
  console.log(`✅ Client package.json mis à jour`);
}

// Mettre à jour server/package.json
const serverPackagePath = path.join('server', 'package.json');
if (fs.existsSync(serverPackagePath)) {
  const serverPackage = JSON.parse(fs.readFileSync(serverPackagePath, 'utf8'));
  serverPackage.version = version;
  fs.writeFileSync(serverPackagePath, JSON.stringify(serverPackage, null, 2) + '\n');
  console.log(`✅ Server package.json mis à jour`);
}

// Créer/mettre à jour le fichier version.json pour le client
const versionInfo = {
  version: version,
  buildDate: new Date().toISOString(),
  gitCommit: process.env.GITHUB_SHA || 'dev'
};

const versionPath = path.join('client', 'public', 'version.json');
const publicDir = path.join('client', 'public');

// Créer le dossier public s'il n'existe pas
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

fs.writeFileSync(versionPath, JSON.stringify(versionInfo, null, 2) + '\n');
console.log(`✅ Fichier version.json créé`);

console.log(`🎉 Toutes les versions ont été mises à jour vers ${version}`);
