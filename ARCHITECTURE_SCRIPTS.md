# 🏗️ Architecture des Scripts - Ma Compta Perso

## 📋 Vue d'ensemble

L'architecture des scripts a été complètement réorganisée pour plus de clarté et de facilité d'utilisation.

## 🎯 Problème résolu

**Avant** : Scripts mélangés à la racine, confusion entre scripts de fonctionnement et outils de développement.

**Maintenant** : Architecture claire avec séparation des responsabilités.

## 🏗️ Nouvelle structure

```
MaComptaPerso/
├── 📁 bin/                     # Scripts de fonctionnement quotidien
│   ├── dev                     # Mode développement (nodemon + vite dev)
│   ├── start                   # Mode production (node + vite preview)
│   ├── stop                    # Arrêt propre de l'application
│   ├── restart-client          # Redémarrage client uniquement
│   ├── restart-server          # Redémarrage serveur uniquement
│   └── README.md               # Documentation des scripts quotidiens
│
├── 📁 dev-tools/               # Outils de développement et maintenance
│   ├── release.sh              # Gestion automatisée des releases
│   ├── generate-changelog.js   # Génération du changelog
│   ├── update-versions.js      # Synchronisation des versions
│   └── README.md               # Documentation des outils de dev
│
├── 📁 deployment/              # Scripts de déploiement et installation
│   ├── install-macompta.sh     # Installation automatique utilisateur final
│   ├── start-production.sh     # Démarrage production optimisé
│   ├── stop-production.sh      # Arrêt production
│   └── README.md               # Documentation de déploiement
│
├── 🚀 macompta.sh              # Script principal unifié
├── 🔗 dev.sh                   # Raccourci → bin/dev
├── 🔗 start.sh                 # Raccourci → bin/start
└── 🔗 stop.sh                  # Raccourci → bin/stop
```

## 🎯 Séparation des responsabilités

### 📁 `bin/` - Scripts de fonctionnement quotidien
**Utilisateurs** : Développeurs et utilisateurs avancés  
**Fonction** : Démarrer, arrêter, redémarrer l'application  
**Fréquence** : Usage quotidien  

### 📁 `dev-tools/` - Outils de développement
**Utilisateurs** : Développeurs et mainteneurs  
**Fonction** : Releases, changelog, maintenance du code  
**Fréquence** : Occasionnelle (releases)  

### 📁 `deployment/` - Scripts de déploiement
**Utilisateurs** : Utilisateurs finaux et administrateurs système  
**Fonction** : Installation, déploiement en production  
**Fréquence** : Une fois par installation  

## 🚀 Script principal `macompta.sh`

Interface unifiée pour toutes les opérations :

```bash
# Fonctionnement quotidien
./macompta.sh dev              # Mode développement
./macompta.sh start            # Mode production
./macompta.sh stop             # Arrêt
./macompta.sh restart          # Redémarrage complet
./macompta.sh status           # Statut de l'application
./macompta.sh logs             # Logs en temps réel

# Développement
./macompta.sh release patch    # Release patch (1.0.0 → 1.0.1)
./macompta.sh release minor    # Release minor (1.0.1 → 1.1.0)
./macompta.sh release major    # Release major (1.1.0 → 2.0.0)
./macompta.sh changelog        # Générer changelog
./macompta.sh sync-versions    # Synchroniser versions

# Déploiement
./macompta.sh install          # Installation automatique
./macompta.sh start-prod       # Production optimisée
./macompta.sh stop-prod        # Arrêt production
```

## 🔗 Raccourcis de compatibilité

Pour maintenir la compatibilité avec les habitudes existantes :

```bash
./dev.sh      # → ./bin/dev
./start.sh    # → ./bin/start  
./stop.sh     # → ./bin/stop
```

## 📊 Avantages de cette architecture

### ✅ **Clarté**
- Chaque script a un rôle bien défini
- Séparation claire des responsabilités
- Documentation dédiée par catégorie

### ✅ **Facilité d'utilisation**
- Script principal unifié (`macompta.sh`)
- Raccourcis pour les commandes fréquentes
- Aide intégrée (`./macompta.sh help`)

### ✅ **Maintenabilité**
- Scripts organisés par fonction
- Documentation à jour
- Évolutivité facilitée

### ✅ **Compatibilité**
- Raccourcis pour les anciens scripts
- Migration transparente
- Pas de rupture de compatibilité

## 🎯 Cas d'usage

### 👨‍💻 Développeur quotidien
```bash
./macompta.sh dev              # Démarrage développement
./macompta.sh logs             # Surveillance des logs
./macompta.sh stop             # Arrêt en fin de journée
```

### 🚀 Création d'une release
```bash
./macompta.sh release minor    # Nouvelle version
# Le script fait tout automatiquement :
# ✅ Incrémente la version
# ✅ Met à jour les package.json
# ✅ Génère le changelog
# ✅ Crée le commit et le tag
# ✅ Propose le push
```

### 👤 Utilisateur final
```bash
./macompta.sh install          # Installation complète
# Puis utilisation via l'icône desktop créée
```

### 🔧 Administrateur système
```bash
./macompta.sh start-prod       # Production optimisée
./macompta.sh status           # Vérification du statut
./macompta.sh logs             # Surveillance
```

## 📚 Documentation

Chaque dossier contient sa propre documentation :

- **`bin/README.md`** - Scripts de fonctionnement quotidien
- **`dev-tools/README.md`** - Outils de développement et releases
- **`deployment/README.md`** - Scripts de déploiement et installation
- **`SCRIPTS.md`** - Vue d'ensemble de tous les scripts
- **`docs/IMPLEMENTATION.md`** - Guide complet de déploiement

## 🔄 Migration depuis l'ancienne architecture

### Anciens scripts → Nouveaux scripts

| Ancien | Nouveau | Fonction |
|--------|---------|----------|
| `./dev.sh` | `./macompta.sh dev` | Mode développement |
| `./restart-all.sh` | `./macompta.sh start` | Démarrage production |
| `./stop-all.sh` | `./macompta.sh stop` | Arrêt application |
| `./release.sh` | `./macompta.sh release` | Gestion des releases |
| `./scripts/generate-changelog.js` | `./macompta.sh changelog` | Génération changelog |

### Compatibilité maintenue

Les raccourcis `./dev.sh`, `./start.sh`, `./stop.sh` continuent de fonctionner.

## 🎉 Résultat

Une architecture claire, documentée et facile à utiliser qui sépare :
- ⚙️ **Fonctionnement quotidien** (`bin/`)
- 🛠️ **Développement** (`dev-tools/`)  
- 🚀 **Déploiement** (`deployment/`)
- 🎯 **Interface unifiée** (`macompta.sh`)

Cette organisation facilite la maintenance, l'utilisation et l'évolution du projet.
