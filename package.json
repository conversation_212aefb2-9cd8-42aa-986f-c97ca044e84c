{"name": "ma-compta-perso", "version": "0.1.1", "description": "Application de comptabilité personnelle", "private": true, "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm start", "dev:client": "cd client && npm run dev", "build": "cd client && npm run build", "version:patch": "npm version patch && npm run update-versions && npm run generate-changelog", "version:minor": "npm version minor && npm run update-versions && npm run generate-changelog", "version:major": "npm version major && npm run update-versions && npm run generate-changelog", "update-versions": "node scripts/update-versions.js", "generate-changelog": "node scripts/generate-changelog.js", "release": "npm run build && git add . && git commit -m \"chore: release v$(node -p \"require('./package.json').version\")\" && git tag v$(node -p \"require('./package.json').version\")", "install:all": "npm install && cd client && npm install && cd ../server && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["client", "server"]}