import { useState, useEffect } from 'react';

export function useVersion() {
  const [version, setVersion] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchVersion = async () => {
      try {
        const response = await fetch('/version.json');
        if (!response.ok) {
          throw new Error('Failed to fetch version');
        }
        const versionData = await response.json();
        setVersion(versionData);
      } catch (err) {
        console.warn('Could not fetch version info:', err);
        // Fallback version
        setVersion({
          version: '1.0.0',
          buildDate: new Date().toISOString(),
          gitCommit: 'unknown'
        });
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchVersion();
  }, []);

  return { version, loading, error };
}
