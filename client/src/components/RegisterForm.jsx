import React, { useState } from 'react'
import { useVersion } from '../hooks/useVersion'

export default function RegisterForm({ onRegister, onShowLogin, apiBase }) {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const { version } = useVersion()

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    // Validation côté client
    if (username.length < 3) {
      setError('Le nom d\'utilisateur doit contenir au moins 3 caractères')
      setLoading(false)
      return
    }

    if (password.length < 6) {
      setError('Le mot de passe doit contenir au moins 6 caractères')
      setLoading(false)
      return
    }

    if (password !== confirmPassword) {
      setError('Les mots de passe ne correspondent pas')
      setLoading(false)
      return
    }

    try {
      const response = await fetch(`${apiBase}/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
      })

      const data = await response.json()

      if (response.ok) {
        onRegister(data.username)
      } else {
        setError(data.error || 'Erreur lors de la création du compte')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="login-container">
      <div className="login-card">
        <h1>Binou Compta Perso</h1>
        <h2>Créer un compte</h2>
        
        {error && <div className="error-message">{error}</div>}
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="username">Nom d'utilisateur</label>
            <input
              id="username"
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              disabled={loading}
              placeholder="Choisissez un nom d'utilisateur"
              minLength={3}
            />
            <small style={{ color: '#666', fontSize: '0.8rem' }}>
              Au moins 3 caractères
            </small>
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Mot de passe</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
              placeholder="Choisissez un mot de passe"
              minLength={6}
            />
            <small style={{ color: '#666', fontSize: '0.8rem' }}>
              Au moins 6 caractères
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword">Confirmer le mot de passe</label>
            <input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              disabled={loading}
              placeholder="Confirmez votre mot de passe"
            />
          </div>
          
          <button type="submit" disabled={loading} className="login-button">
            {loading ? 'Création en cours...' : 'Créer mon compte'}
          </button>
        </form>
        
        <div className="register-link">
          <p>Déjà un compte ?</p>
          <button 
            type="button" 
            onClick={onShowLogin}
            className="register-button"
            disabled={loading}
          >
            Se connecter
          </button>
        </div>
        
        <div className="login-info">
          <p><strong>Version :</strong> {version?.version || '1.0.0'}</p>
          {version?.buildDate && (
            <p><small>Build : {new Date(version.buildDate).toLocaleDateString('fr-FR')}</small></p>
          )}
        </div>
      </div>
    </div>
  )
}
