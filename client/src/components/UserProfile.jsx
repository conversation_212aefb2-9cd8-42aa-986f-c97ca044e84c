import React, { useState } from 'react'

export default function UserProfile({ user, onClose, onLogout, apiBase, authToken }) {
  const [activeTab, setActiveTab] = useState('info')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // États pour la modification du profil
  const [newUsername, setNewUsername] = useState(user)
  
  // États pour le changement de mot de passe
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmNewPassword, setConfirmNewPassword] = useState('')

  // États pour la suppression du compte
  const [deleteConfirmation, setDeleteConfirmation] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  const clearMessages = () => {
    setError('')
    setSuccess('')
  }

  const handleUpdateProfile = async (e) => {
    e.preventDefault()
    clearMessages()
    setLoading(true)

    if (newUsername.length < 3) {
      setError('Le nom d\'utilisateur doit contenir au moins 3 caractères')
      setLoading(false)
      return
    }

    try {
      const response = await fetch(`${apiBase}/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({ username: newUsername })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Profil mis à jour avec succès')
        // Note: Dans une vraie app, il faudrait mettre à jour le nom d'utilisateur dans l'état global
      } else {
        setError(data.error || 'Erreur lors de la mise à jour du profil')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  const handleChangePassword = async (e) => {
    e.preventDefault()
    clearMessages()
    setLoading(true)

    if (newPassword.length < 6) {
      setError('Le nouveau mot de passe doit contenir au moins 6 caractères')
      setLoading(false)
      return
    }

    if (newPassword !== confirmNewPassword) {
      setError('Les nouveaux mots de passe ne correspondent pas')
      setLoading(false)
      return
    }

    try {
      const response = await fetch(`${apiBase}/profile/password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({ 
          currentPassword, 
          newPassword 
        })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Mot de passe modifié avec succès')
        setCurrentPassword('')
        setNewPassword('')
        setConfirmNewPassword('')
      } else {
        setError(data.error || 'Erreur lors du changement de mot de passe')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteAccount = async () => {
    if (deleteConfirmation !== 'SUPPRIMER') {
      setError('Veuillez taper "SUPPRIMER" pour confirmer')
      return
    }

    clearMessages()
    setLoading(true)

    try {
      const response = await fetch(`${apiBase}/profile`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Compte supprimé avec succès')
        // Déconnecter l'utilisateur après suppression
        setTimeout(() => {
          onLogout()
        }, 2000)
      } else {
        setError(data.error || 'Erreur lors de la suppression du compte')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="profile-overlay">
      <div className="profile-modal">
        <div className="profile-header">
          <h2>👤 Mon Profil</h2>
          <button onClick={onClose} className="close-button">✕</button>
        </div>

        {error && <div className="error-message">{error}</div>}
        {success && <div className="success-message">{success}</div>}

        <div className="profile-tabs">
          <button 
            className={`tab-button ${activeTab === 'info' ? 'active' : ''}`}
            onClick={() => setActiveTab('info')}
          >
            📝 Informations
          </button>
          <button 
            className={`tab-button ${activeTab === 'password' ? 'active' : ''}`}
            onClick={() => setActiveTab('password')}
          >
            🔒 Mot de passe
          </button>
          <button 
            className={`tab-button ${activeTab === 'delete' ? 'active' : ''}`}
            onClick={() => setActiveTab('delete')}
          >
            🗑️ Supprimer
          </button>
        </div>

        <div className="profile-content">
          {activeTab === 'info' && (
            <form onSubmit={handleUpdateProfile}>
              <div className="form-group">
                <label htmlFor="username">Nom d'utilisateur</label>
                <input
                  id="username"
                  type="text"
                  value={newUsername}
                  onChange={(e) => setNewUsername(e.target.value)}
                  required
                  disabled={loading}
                  minLength={3}
                />
              </div>
              <button type="submit" disabled={loading} className="primary-button">
                {loading ? 'Mise à jour...' : 'Mettre à jour'}
              </button>
            </form>
          )}

          {activeTab === 'password' && (
            <form onSubmit={handleChangePassword}>
              <div className="form-group">
                <label htmlFor="currentPassword">Mot de passe actuel</label>
                <input
                  id="currentPassword"
                  type="password"
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>
              <div className="form-group">
                <label htmlFor="newPassword">Nouveau mot de passe</label>
                <input
                  id="newPassword"
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  disabled={loading}
                  minLength={6}
                />
              </div>
              <div className="form-group">
                <label htmlFor="confirmNewPassword">Confirmer le nouveau mot de passe</label>
                <input
                  id="confirmNewPassword"
                  type="password"
                  value={confirmNewPassword}
                  onChange={(e) => setConfirmNewPassword(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>
              <button type="submit" disabled={loading} className="primary-button">
                {loading ? 'Modification...' : 'Changer le mot de passe'}
              </button>
            </form>
          )}

          {activeTab === 'delete' && (
            <div className="delete-section">
              <div className="warning-box">
                <h3>⚠️ Attention</h3>
                <p>Cette action est irréversible. Toutes vos données (transactions, catégories) seront définitivement supprimées.</p>
              </div>
              
              {!showDeleteConfirm ? (
                <button 
                  onClick={() => setShowDeleteConfirm(true)}
                  className="danger-button"
                  disabled={loading}
                >
                  Supprimer mon compte
                </button>
              ) : (
                <div className="delete-confirm">
                  <p>Pour confirmer la suppression, tapez <strong>SUPPRIMER</strong> :</p>
                  <input
                    type="text"
                    value={deleteConfirmation}
                    onChange={(e) => setDeleteConfirmation(e.target.value)}
                    placeholder="Tapez SUPPRIMER"
                    disabled={loading}
                  />
                  <div className="delete-buttons">
                    <button 
                      onClick={() => {
                        setShowDeleteConfirm(false)
                        setDeleteConfirmation('')
                      }}
                      className="secondary-button"
                      disabled={loading}
                    >
                      Annuler
                    </button>
                    <button 
                      onClick={handleDeleteAccount}
                      className="danger-button"
                      disabled={loading || deleteConfirmation !== 'SUPPRIMER'}
                    >
                      {loading ? 'Suppression...' : 'Confirmer la suppression'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
