import React, { useState, useEffect, useRef } from 'react'

export default function CategoryManager({ categories, onCategoryAdded, onCategoryDeleted, apiBase, authToken }) {
  const [newCategoryName, setNewCategoryName] = useState('')
  const [selectedEmoji, setSelectedEmoji] = useState('')
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [initLoading, setInitLoading] = useState(false)
  const emojiPickerRef = useRef(null)

  // Liste d'emojis organisés par catégories
  const emojiCategories = {
    'Argent & Finance': ['💰', '💳', '💵', '💴', '💶', '💷', '🏦', '💎', '🪙', '💸'],
    'Maison & Logement': ['🏠', '🏡', '🏢', '🏬', '🏪', '🏨', '🏘️', '🏰', '🏗️', '🏭'],
    'Transport': ['🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🚁', '✈️', '🚀', '🚂', '🚆', '🚄', '🚅', '🚈', '🚝', '🚞', '🚋', '🚃', '🚟', '🚠', '🚡', '⛵', '🛥️', '🚤', '⛴️', '🛳️', '🚢'],
    'Alimentation': ['🍕', '🍔', '🍟', '🌭', '🥪', '🌮', '🌯', '🥙', '🧆', '🥚', '🍳', '🥘', '🍲', '🥗', '🍿', '🧈', '🍞', '🥖', '🥨', '🧀', '🥓', '🥩', '🍗', '🍖', '🦴', '🌶️', '🥕', '🌽', '🥒', '🥬', '🥦', '🧄', '🧅', '🍄', '🥜', '🌰', '🍞', '🥐', '🥯', '🍰', '🧁', '🥧', '🍮', '🍭', '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥛', '🍼', '☕', '🍵', '🧃', '🥤', '🍶', '🍺', '🍻', '🥂', '🍷', '🥃', '🍸', '🍹', '🧉', '🍾'],
    'Shopping & Achats': ['🛒', '🛍️', '🎁', '🏪', '🏬', '🛒', '💳', '🧾', '🏷️', '💰'],
    'Santé & Bien-être': ['🏥', '💊', '🩺', '💉', '🦷', '👩‍⚕️', '👨‍⚕️', '🧘', '🏃', '🚴', '🏋️', '🤸', '🧖', '💆', '💇'],
    'Éducation & Travail': ['📚', '📖', '📝', '✏️', '📏', '📐', '🖊️', '🖋️', '✒️', '🖌️', '🖍️', '📄', '📃', '📑', '📊', '📈', '📉', '🗂️', '📂', '📁', '📋', '📌', '📍', '📎', '🖇️', '📐', '📏', '📖', '📚', '📓', '📔', '📒', '📕', '📗', '📘', '📙', '💼', '👔', '🏢', '💻', '⌨️', '🖥️', '🖨️', '📱', '☎️', '📞', '📟', '📠', '🔍', '🔎'],
    'Loisirs & Divertissement': ['🎬', '🎭', '🎪', '🎨', '🎰', '🎲', '🎯', '🎳', '🎮', '🕹️', '🎺', '🎷', '🎸', '🎹', '🥁', '🎤', '🎧', '📻', '🎵', '🎶', '🎼', '🎹', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️', '🎫', '🎟️', '🎪', '🎨', '🖼️', '🎭', '🎪'],
    'Vêtements & Mode': ['👕', '👔', '👗', '👙', '👚', '👖', '🩳', '👘', '🥻', '🩱', '🩲', '🩳', '👒', '🧢', '👑', '💍', '💎', '👜', '👛', '👝', '🎒', '👞', '👟', '🥾', '🥿', '👠', '👡', '🩴', '👢', '👓', '🕶️', '🥽', '⌚', '📿', '💄', '💅'],
    'Technologie': ['💻', '🖥️', '🖨️', '⌨️', '🖱️', '🖲️', '💽', '💾', '💿', '📀', '📱', '☎️', '📞', '📟', '📠', '📺', '📻', '🎙️', '🎚️', '🎛️', '🧭', '⏱️', '⏲️', '⏰', '🕰️', '⌚', '📡', '🔋', '🔌', '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸'],
    'Nature & Animaux': ['🌱', '🌿', '☘️', '🍀', '🎋', '🎍', '🌾', '🌵', '🌲', '🌳', '🌴', '🌸', '🌺', '🌻', '🌹', '🥀', '🌷', '🌼', '🌻', '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜', '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕', '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳', '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🦧', '🐘', '🦛', '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖', '🐏', '🐑', '🦙', '🐐', '🦌', '🐕', '🐩', '🦮', '🐕‍🦺', '🐈', '🐓', '🦃', '🦚', '🦜', '🦢', '🦩', '🕊️', '🐇', '🦝', '🦨', '🦡', '🦦', '🦥', '🐁', '🐀', '🐿️', '🦔'],
    'Sport & Activités': ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️‍♀️', '🏋️', '🏋️‍♂️', '🤼‍♀️', '🤼', '🤼‍♂️', '🤸‍♀️', '🤸', '🤸‍♂️', '⛹️‍♀️', '⛹️', '⛹️‍♂️', '🤺', '🤾‍♀️', '🤾', '🤾‍♂️', '🏌️‍♀️', '🏌️', '🏌️‍♂️', '🏇', '🧘‍♀️', '🧘', '🧘‍♂️', '🏄‍♀️', '🏄', '🏄‍♂️', '🏊‍♀️', '🏊', '🏊‍♂️', '🤽‍♀️', '🤽', '🤽‍♂️', '🚣‍♀️', '🚣', '🚣‍♂️', '🧗‍♀️', '🧗', '🧗‍♂️', '🚵‍♀️', '🚵', '🚵‍♂️', '🚴‍♀️', '🚴', '🚴‍♂️', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️'],
    'Voyage & Vacances': ['✈️', '🛫', '🛬', '🪂', '💺', '🚁', '🚟', '🚠', '🚡', '🛰️', '🚀', '🛸', '🚂', '🚆', '🚄', '🚅', '🚈', '🚝', '🚞', '🚋', '🚃', '🚎', '🚌', '🚐', '🚑', '🚒', '🚓', '🚔', '🚕', '🚖', '🚗', '🚘', '🚙', '🛻', '🚚', '🚛', '🚜', '🏎️', '🏍️', '🛵', '🦽', '🦼', '🛴', '🚲', '🛹', '🛼', '🚁', '⛵', '🛥️', '🚤', '⛴️', '🛳️', '🚢', '⚓', '⛽', '🚨', '🚥', '🚦', '🛑', '🚧', '⚠️', '🏁', '🏳️', '🏴', '🏳️‍🌈', '🏳️‍⚧️', '🏴‍☠️', '🇦🇨', '🗺️', '🗾', '🧭', '🏔️', '⛰️', '🌋', '🗻', '🏕️', '🏖️', '🏜️', '🏝️', '🏞️', '🏟️', '🏛️', '🏗️', '🧱', '🪨', '🪵', '🛖', '🏘️', '🏚️', '🏠', '🏡', '🏢', '🏣', '🏤', '🏥', '🏦', '🏨', '🏩', '🏪', '🏫', '🏬', '🏭', '🏯', '🏰', '🗼', '🗽', '⛪', '🕌', '🛕', '🕍', '⛩️', '🕋', '⛲', '⛺', '🌁', '🌃', '🏙️', '🌄', '🌅', '🌆', '🌇', '🌉', '♨️', '🎠', '🎡', '🎢', '💈', '🎪'],
    'Autres': ['❤️', '💙', '💚', '💛', '🧡', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '📴', '📳', '🈶', '🈚', '🈸', '🈺', '🈷️', '✴️', '🆚', '💮', '🉐', '㊙️', '㊗️', '🈴', '🈵', '🈹', '🈲', '🅰️', '🅱️', '🆎', '🆑', '🅾️', '🆘', '❌', '⭕', '🛑', '⛔', '📛', '🚫', '💯', '💢', '♨️', '🚷', '🚯', '🚳', '🚱', '🔞', '📵', '🚭', '❗', '❕', '❓', '❔', '‼️', '⁉️', '🔅', '🔆', '〽️', '⚠️', '🚸', '🔱', '⚜️', '🔰', '♻️', '✅', '🈯', '💹', '❇️', '✳️', '❎', '🌐', '💠', 'Ⓜ️', '🌀', '💤', '🏧', '🚾', '♿', '🅿️', '🈳', '🈂️', '🛂', '🛃', '🛄', '🛅', '🚹', '🚺', '🚼', '🚻', '🚮', '🎦', '📶', '🈁', '🔣', 'ℹ️', '🔤', '🔡', '🔠', '🆖', '🆗', '🆙', '🆒', '🆕', '🆓', '0️⃣', '1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟']
  }

  // Fermer le sélecteur d'emoji quand on clique ailleurs
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
        setShowEmojiPicker(false)
      }
    }

    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showEmojiPicker])

  const handleEmojiSelect = (emoji) => {
    setSelectedEmoji(emoji)
    setShowEmojiPicker(false)
    // Si le nom de catégorie est vide, on met juste l'emoji
    if (!newCategoryName.trim()) {
      setNewCategoryName(emoji + ' ')
    } else {
      // Si le nom commence déjà par un emoji, on le remplace
      const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u
      if (emojiRegex.test(newCategoryName)) {
        setNewCategoryName(emoji + newCategoryName.substring(1))
      } else {
        setNewCategoryName(emoji + ' ' + newCategoryName)
      }
    }
  }

  const handleAddCategory = async (e) => {
    e.preventDefault()
    if (!newCategoryName.trim()) return

    setLoading(true)
    setError('')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/categories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ name: newCategoryName.trim() })
      })

      const data = await response.json()

      if (response.ok) {
        setNewCategoryName('')
        setSelectedEmoji('')
        onCategoryAdded()
      } else {
        setError(data.error || 'Erreur lors de l\'ajout')
      }
    } catch (error) {
      setError('Erreur de connexion')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteCategory = async (categoryId, categoryName) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${categoryName}" ?\n\nAttention : Cette action est irréversible.`)) {
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/categories/${categoryId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        onCategoryDeleted()
      } else {
        const data = await response.json()
        alert(data.error || 'Erreur lors de la suppression')
      }
    } catch (error) {
      alert('Erreur de connexion')
    }
  }

  const handleInitDefaults = async () => {
    if (!confirm('Voulez-vous ajouter les catégories par défaut ?\n\nCela ajoutera 20 catégories courantes comme Logement, Transport, Alimentation, etc.')) {
      return
    }

    setInitLoading(true)
    setError('')

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/categories/init-defaults`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()

      if (response.ok) {
        onCategoryAdded()
        alert(`✅ ${data.created} catégories ajoutées avec succès !`)
      } else {
        setError(data.error || 'Erreur lors de l\'initialisation')
      }
    } catch (error) {
      setError('Erreur de connexion')
    } finally {
      setInitLoading(false)
    }
  }

  return (
    <div className="category-manager">
      <div className="dashboard-header">
        <h2>🏷️ Gestion des catégories</h2>
        <p>Organisez vos transactions avec des catégories personnalisées</p>
      </div>

      {/* Formulaire d'ajout */}
      <div className="card">
        <h3>➕ Ajouter une nouvelle catégorie</h3>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <form onSubmit={handleAddCategory} className="category-form">
          <div className="form-row" style={{ display: 'flex', gap: '8px', alignItems: 'flex-start' }}>
            {/* Bouton sélecteur d'emoji */}
            <div ref={emojiPickerRef} style={{ position: 'relative' }}>
              <button
                type="button"
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                disabled={loading}
                style={{
                  padding: '12px',
                  fontSize: '20px',
                  border: '2px solid #ddd',
                  borderRadius: '8px',
                  backgroundColor: '#f8f9fa',
                  cursor: 'pointer',
                  minWidth: '50px',
                  height: '50px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                title="Choisir un emoji"
              >
                {selectedEmoji || '😀'}
              </button>

              {/* Sélecteur d'emoji */}
              {showEmojiPicker && (
                <div style={{
                  position: 'absolute',
                  top: '55px',
                  left: '0',
                  backgroundColor: 'white',
                  border: '2px solid #ddd',
                  borderRadius: '12px',
                  padding: '16px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  zIndex: 1000,
                  width: '400px',
                  maxHeight: '300px',
                  overflowY: 'auto'
                }}>
                  <div style={{ marginBottom: '12px', fontWeight: 'bold', color: '#333' }}>
                    Choisir un emoji :
                  </div>
                  {Object.entries(emojiCategories).map(([categoryName, emojis]) => (
                    <div key={categoryName} style={{ marginBottom: '16px' }}>
                      <div style={{
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: '#666',
                        marginBottom: '8px',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px'
                      }}>
                        {categoryName}
                      </div>
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(10, 1fr)',
                        gap: '4px'
                      }}>
                        {emojis.slice(0, 20).map((emoji, index) => (
                          <button
                            key={index}
                            type="button"
                            onClick={() => handleEmojiSelect(emoji)}
                            style={{
                              padding: '6px',
                              fontSize: '18px',
                              border: 'none',
                              borderRadius: '6px',
                              backgroundColor: selectedEmoji === emoji ? '#e3f2fd' : 'transparent',
                              cursor: 'pointer',
                              transition: 'background-color 0.2s',
                              aspectRatio: '1'
                            }}
                            onMouseEnter={(e) => e.target.style.backgroundColor = '#f0f0f0'}
                            onMouseLeave={(e) => e.target.style.backgroundColor = selectedEmoji === emoji ? '#e3f2fd' : 'transparent'}
                            title={emoji}
                          >
                            {emoji}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                  <div style={{ textAlign: 'center', marginTop: '12px' }}>
                    <button
                      type="button"
                      onClick={() => setShowEmojiPicker(false)}
                      style={{
                        padding: '8px 16px',
                        fontSize: '12px',
                        border: '1px solid #ddd',
                        borderRadius: '6px',
                        backgroundColor: '#f8f9fa',
                        cursor: 'pointer'
                      }}
                    >
                      Fermer
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Champ de saisie du nom */}
            <input
              type="text"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Nom de la catégorie (ex: Restaurant)"
              disabled={loading}
              maxLength="50"
              style={{ flex: 1 }}
            />

            {/* Bouton d'ajout */}
            <button
              type="submit"
              disabled={loading || !newCategoryName.trim()}
              className="success"
              style={{ whiteSpace: 'nowrap' }}
            >
              {loading ? '⏳ Ajout...' : '✅ Ajouter'}
            </button>
          </div>
        </form>
      </div>

      {/* Initialisation des catégories par défaut */}
      {categories.length === 0 && (
        <div className="card init-card">
          <h3>🚀 Démarrage rapide</h3>
          <p>Vous n'avez encore aucune catégorie. Voulez-vous ajouter les catégories les plus courantes ?</p>
          <div style={{ textAlign: 'center', marginTop: '16px' }}>
            <button
              onClick={handleInitDefaults}
              disabled={initLoading}
              className="success"
              style={{ fontSize: '16px', padding: '12px 24px' }}
            >
              {initLoading ? '⏳ Ajout en cours...' : '✨ Ajouter les catégories par défaut'}
            </button>
          </div>
          <div style={{ fontSize: '0.9rem', color: '#666', marginTop: '12px', textAlign: 'center' }}>
            Cela ajoutera 20 catégories comme : 🏠 Logement, 🚗 Transport, 🍽️ Alimentation, etc.
          </div>
        </div>
      )}

      {/* Liste des catégories */}
      <div className="card">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3>📋 Vos catégories ({categories.length})</h3>
          {categories.length > 0 && (
            <button
              onClick={handleInitDefaults}
              disabled={initLoading}
              className="success"
              style={{ fontSize: '12px', padding: '6px 12px' }}
            >
              {initLoading ? '⏳ Ajout...' : '➕ Ajouter catégories par défaut'}
            </button>
          )}
        </div>
        
        {categories.length === 0 ? (
          <div className="empty-state">
            <div className="empty-state-icon">🏷️</div>
            <p>Aucune catégorie pour le moment</p>
            <p style={{ fontSize: '0.9rem', color: '#666' }}>
              Ajoutez votre première catégorie ci-dessus
            </p>
          </div>
        ) : (
          <div className="categories-grid">
            {categories.map(category => (
              <div key={category.id} className="category-item">
                <div className="category-info">
                  <span className="category-name">{category.name}</span>
                </div>
                <button
                  onClick={() => handleDeleteCategory(category.id, category.name)}
                  className="delete-category-btn"
                  title="Supprimer cette catégorie"
                >
                  🗑️
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Conseils */}
      <div className="card tips-card">
        <h3>💡 Conseils</h3>
        <ul className="tips-list">
          <li>Cliquez sur le bouton emoji 😀 pour choisir facilement une icône</li>
          <li>Les emojis rendent vos catégories plus visuelles et faciles à identifier</li>
          <li>Créez des catégories spécifiques pour un meilleur suivi</li>
          <li>Les catégories supprimées ne peuvent pas être récupérées</li>
          <li>Vous pouvez avoir jusqu'à 50 caractères par nom de catégorie</li>
        </ul>
      </div>
    </div>
  )
}
