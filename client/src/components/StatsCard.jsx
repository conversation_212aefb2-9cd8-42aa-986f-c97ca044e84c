import React from 'react'

export default function StatsCard({ transactions }) {
  if (!transactions || transactions.length === 0) {
    return null
  }

  // Calculs des statistiques
  const totalTransactions = transactions.length
  const incomeTransactions = transactions.filter(t => t.type === 'in')
  const expenseTransactions = transactions.filter(t => t.type === 'out')
  
  const avgIncome = incomeTransactions.length > 0 
    ? incomeTransactions.reduce((sum, t) => sum + t.amount, 0) / incomeTransactions.length 
    : 0
    
  const avgExpense = expenseTransactions.length > 0 
    ? expenseTransactions.reduce((sum, t) => sum + t.amount, 0) / expenseTransactions.length 
    : 0

  // Catégorie la plus utilisée
  const categoryStats = {}
  transactions.forEach(t => {
    if (t.category_name) {
      categoryStats[t.category_name] = (categoryStats[t.category_name] || 0) + 1
    }
  })
  
  const topCategory = Object.keys(categoryStats).length > 0 
    ? Object.keys(categoryStats).reduce((a, b) => categoryStats[a] > categoryStats[b] ? a : b)
    : null

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  return (
    <div className="card">
      <h3>📈 Statistiques</h3>
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '16px',
        marginTop: '16px'
      }}>
        <div style={{ 
          padding: '16px', 
          background: '#f8f9fa', 
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', marginBottom: '8px' }}>📊</div>
          <div style={{ fontSize: '1.5rem', fontWeight: '600', color: '#1976d2' }}>
            {totalTransactions}
          </div>
          <div style={{ fontSize: '0.9rem', color: '#666' }}>
            Transaction{totalTransactions > 1 ? 's' : ''} au total
          </div>
        </div>

        <div style={{ 
          padding: '16px', 
          background: '#e8f5e8', 
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', marginBottom: '8px' }}>💰</div>
          <div style={{ fontSize: '1.2rem', fontWeight: '600', color: '#4caf50' }}>
            {formatCurrency(avgIncome)}
          </div>
          <div style={{ fontSize: '0.9rem', color: '#666' }}>
            Entrée moyenne
          </div>
        </div>

        <div style={{ 
          padding: '16px', 
          background: '#ffebee', 
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', marginBottom: '8px' }}>💸</div>
          <div style={{ fontSize: '1.2rem', fontWeight: '600', color: '#f44336' }}>
            {formatCurrency(avgExpense)}
          </div>
          <div style={{ fontSize: '0.9rem', color: '#666' }}>
            Dépense moyenne
          </div>
        </div>

        {topCategory && (
          <div style={{ 
            padding: '16px', 
            background: '#fff3e0', 
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '8px' }}>🏷️</div>
            <div style={{ fontSize: '1rem', fontWeight: '600', color: '#ff9800' }}>
              {topCategory}
            </div>
            <div style={{ fontSize: '0.9rem', color: '#666' }}>
              Catégorie la plus utilisée
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
