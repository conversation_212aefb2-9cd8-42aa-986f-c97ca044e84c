import React, { useState, useEffect } from 'react'

export default function AccountSettings({ apiBase, authToken, onClose, onSaved }) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  // États du formulaire
  const [accountName, setAccountName] = useState('Mon Compte Principal')
  const [iban, setIban] = useState('')
  const [bankAddress, setBankAddress] = useState('')
  const [initialBalance, setInitialBalance] = useState('')
  const [initialBalanceDate, setInitialBalanceDate] = useState('')

  // Charger les données du compte au montage du composant
  useEffect(() => {
    loadAccountData()
  }, [])

  const loadAccountData = async () => {
    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/account`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const account = await response.json()
        setAccountName(account.account_name || 'Mon Compte Principal')
        setIban(account.iban || '')
        setBankAddress(account.bank_address || '')
        setInitialBalance(account.initial_balance?.toString() || '0')
        
        // Formater la date pour l'input date
        if (account.initial_balance_date) {
          const date = new Date(account.initial_balance_date)
          setInitialBalanceDate(date.toISOString().split('T')[0])
        } else {
          setInitialBalanceDate(new Date().toISOString().split('T')[0])
        }
      } else {
        setError('Erreur lors du chargement des données du compte')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setSuccess('')
    setLoading(true)

    // Validation
    if (!accountName.trim()) {
      setError('Le nom du compte est requis')
      setLoading(false)
      return
    }

    const balance = parseFloat(initialBalance)
    if (isNaN(balance)) {
      setError('Le solde initial doit être un nombre valide')
      setLoading(false)
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/account`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          account_name: accountName.trim(),
          iban: iban.trim(),
          bank_address: bankAddress.trim(),
          initial_balance: balance,
          initial_balance_date: initialBalanceDate ? new Date(initialBalanceDate).toISOString() : new Date().toISOString()
        })
      })

      if (response.ok) {
        setSuccess('Configuration du compte mise à jour avec succès !')
        if (onSaved) {
          onSaved()
        }
        // Fermer automatiquement après 2 secondes
        setTimeout(() => {
          if (onClose) {
            onClose()
          }
        }, 2000)
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de la mise à jour')
      }
    } catch (err) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  const formatIban = (value) => {
    // Supprimer tous les espaces et convertir en majuscules
    const cleaned = value.replace(/\s/g, '').toUpperCase()
    // Ajouter un espace tous les 4 caractères
    return cleaned.replace(/(.{4})/g, '$1 ').trim()
  }

  const handleIbanChange = (e) => {
    const formatted = formatIban(e.target.value)
    setIban(formatted)
  }

  return (
    <div className="profile-overlay">
      <div className="profile-modal account-settings-modal">
        <div className="profile-header">
          <h2>⚙️ Configuration du Compte</h2>
          <button onClick={onClose} className="close-button">✕</button>
        </div>

        {error && <div className="error-message">{error}</div>}
        {success && <div className="success-message">{success}</div>}

        <form onSubmit={handleSubmit} className="account-settings-form">
          <div className="form-section">
            <h3>🏦 Informations du compte</h3>
            
            <div className="form-group">
              <label htmlFor="accountName">Nom du compte *</label>
              <input
                id="accountName"
                type="text"
                value={accountName}
                onChange={(e) => setAccountName(e.target.value)}
                placeholder="ex: LCL Compte Courant"
                required
                disabled={loading}
              />
            </div>

            <div className="form-group">
              <label htmlFor="iban">IBAN</label>
              <input
                id="iban"
                type="text"
                value={iban}
                onChange={handleIbanChange}
                placeholder="ex: FR14 2004 1010 0505 0001 3M02 606"
                maxLength={34}
                disabled={loading}
              />
              <small className="form-help">Format automatique avec espaces</small>
            </div>

            <div className="form-group">
              <label htmlFor="bankAddress">Adresse de l'agence</label>
              <textarea
                id="bankAddress"
                value={bankAddress}
                onChange={(e) => setBankAddress(e.target.value)}
                placeholder="ex: LCL Agence Paris République&#10;123 Boulevard de la République&#10;75003 Paris"
                rows={3}
                disabled={loading}
              />
            </div>
          </div>

          <div className="form-section">
            <h3>💰 Solde initial</h3>
            <p className="form-description">
              Définissez le solde de votre compte à une date donnée pour synchroniser 
              votre comptabilité avec la réalité de votre compte bancaire.
            </p>
            
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="initialBalance">Solde initial *</label>
                <input
                  id="initialBalance"
                  type="number"
                  step="0.01"
                  value={initialBalance}
                  onChange={(e) => setInitialBalance(e.target.value)}
                  placeholder="0.00"
                  required
                  disabled={loading}
                />
              </div>

              <div className="form-group">
                <label htmlFor="initialBalanceDate">Date du solde *</label>
                <input
                  id="initialBalanceDate"
                  type="date"
                  value={initialBalanceDate}
                  onChange={(e) => setInitialBalanceDate(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          <div className="form-actions">
            <button type="button" onClick={onClose} disabled={loading} className="secondary-button">
              Annuler
            </button>
            <button type="submit" disabled={loading} className="primary-button">
              {loading ? '⏳ Enregistrement...' : '✅ Enregistrer'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
