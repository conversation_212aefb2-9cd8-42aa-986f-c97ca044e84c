const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

const DB_DIR = path.join(__dirname, 'data');
if (!fs.existsSync(DB_DIR)) fs.mkdirSync(DB_DIR, { recursive: true });
const DB_PATH = path.join(DB_DIR, 'spc.db');

const db = new sqlite3.Database(DB_PATH);

// Enable WAL mode for better performance
db.run('PRAGMA journal_mode = WAL');

// Create tables if not exists
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT NOT NULL UNIQUE,
      password_hash TEXT NOT NULL,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  db.run(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      user_id INTEGER NOT NULL,
      FOREIGN KEY(user_id) REFERENCES users(id),
      UNIQUE(name, user_id)
    )
  `);

  db.run(`
    CREATE TABLE IF NOT EXISTS transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      amount REAL NOT NULL,
      type TEXT NOT NULL CHECK(type IN ('in','out')),
      category_id INTEGER,
      description TEXT,
      date_iso TEXT NOT NULL,
      user_id INTEGER NOT NULL,
      FOREIGN KEY(category_id) REFERENCES categories(id),
      FOREIGN KEY(user_id) REFERENCES users(id)
    )
  `);

  db.run(`
    CREATE TABLE IF NOT EXISTS backup_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      backup_date TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      status TEXT NOT NULL CHECK(status IN ('success','error')),
      transactions_count INTEGER DEFAULT 0,
      categories_count INTEGER DEFAULT 0,
      backup_file_path TEXT,
      backup_file_size INTEGER DEFAULT 0,
      error_message TEXT,
      FOREIGN KEY(user_id) REFERENCES users(id)
    )
  `);

  // Table pour tracker les modifications de données
  db.run(`
    CREATE TABLE IF NOT EXISTS data_changes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      table_name TEXT NOT NULL,
      last_modified TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY(user_id) REFERENCES users(id),
      UNIQUE(user_id, table_name)
    )
  `);

  // Table pour les comptes bancaires
  db.run(`
    CREATE TABLE IF NOT EXISTS accounts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      account_name TEXT NOT NULL DEFAULT 'Mon Compte',
      iban TEXT,
      bank_address TEXT,
      initial_balance REAL NOT NULL DEFAULT 0,
      initial_balance_date TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY(user_id) REFERENCES users(id),
      UNIQUE(user_id)
    )
  `);

  // Migration : Ajouter les nouvelles colonnes à backup_logs si elles n'existent pas
  db.run(`ALTER TABLE backup_logs ADD COLUMN backup_file_path TEXT`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Erreur lors de l\'ajout de backup_file_path:', err);
    }
  });

  db.run(`ALTER TABLE backup_logs ADD COLUMN backup_file_size INTEGER DEFAULT 0`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Erreur lors de l\'ajout de backup_file_size:', err);
    }
  });

  // Migration : Ajouter les colonnes de pointage à la table transactions
  db.run(`ALTER TABLE transactions ADD COLUMN is_reconciled INTEGER DEFAULT 0`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Erreur lors de l\'ajout de is_reconciled:', err);
    }
  });

  db.run(`ALTER TABLE transactions ADD COLUMN reconciled_date TEXT`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Erreur lors de l\'ajout de reconciled_date:', err);
    }
  });

  db.run(`ALTER TABLE transactions ADD COLUMN reconciled_by INTEGER`, (err) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Erreur lors de l\'ajout de reconciled_by:', err);
    }
  });

  // Créer un utilisateur par défaut si aucun n'existe
  db.get('SELECT COUNT(*) as count FROM users', (err, row) => {
    if (err) {
      console.error('Erreur lors de la vérification des utilisateurs:', err);
      return;
    }

    if (row.count === 0) {
      // Mot de passe par défaut : "password" (à changer en production)
      const defaultPasswordHash = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'; // bcrypt hash pour "password"
      db.run('INSERT INTO users (username, password_hash) VALUES (?, ?)', ['admin', defaultPasswordHash], function(err) {
        if (err) {
          console.error('Erreur lors de la création de l\'utilisateur par défaut:', err);
        } else {
          console.log('Utilisateur par défaut créé: admin / password');
          const userId = this.lastID;

          // Créer les catégories par défaut pour l'utilisateur
          const defaultCategories = [
            '🏠 Logement',
            '🚗 Transport',
            '🍽️ Alimentation',
            '📱 Téléphone',
            '💡 Électricité',
            '💧 Eau',
            '🌐 Internet',
            '🏥 Santé',
            '👕 Vêtements',
            '🎬 Loisirs',
            '📚 Éducation',
            '🎁 Cadeaux',
            '💰 Épargne',
            '🏪 Courses',
            '⛽ Carburant',
            '🔧 Réparations',
            '📄 Assurances',
            '🏦 Banque',
            '💼 Travail',
            '🎯 Autres'
          ];

          defaultCategories.forEach(categoryName => {
            db.run('INSERT INTO categories (name, user_id) VALUES (?, ?)', [categoryName, userId], (err) => {
              if (err) {
                console.error('Erreur lors de la création de la catégorie:', categoryName, err);
              }
            });
          });

          console.log('Catégories par défaut créées');

          // Créer un compte par défaut pour l'utilisateur
          db.run(`INSERT INTO accounts (user_id, account_name, initial_balance, initial_balance_date)
                  VALUES (?, ?, ?, ?)`,
                  [userId, 'Mon Compte Principal', 0, new Date().toISOString()], (err) => {
            if (err) {
              console.error('Erreur lors de la création du compte par défaut:', err);
            } else {
              console.log('Compte par défaut créé');
            }
          });
        }
      });
    }
  });
});

// Fonctions pour la gestion des comptes
function getAccount(userId, callback) {
  db.get('SELECT * FROM accounts WHERE user_id = ?', [userId], callback);
}

function createOrUpdateAccount(userId, accountData, callback) {
  const { account_name, iban, bank_address, initial_balance, initial_balance_date } = accountData;

  // Vérifier si un compte existe déjà
  db.get('SELECT id FROM accounts WHERE user_id = ?', [userId], (err, row) => {
    if (err) return callback(err);

    if (row) {
      // Mettre à jour le compte existant
      db.run(`UPDATE accounts SET
                account_name = ?,
                iban = ?,
                bank_address = ?,
                initial_balance = ?,
                initial_balance_date = ?,
                updated_at = CURRENT_TIMESTAMP
              WHERE user_id = ?`,
              [account_name, iban, bank_address, initial_balance, initial_balance_date, userId],
              callback);
    } else {
      // Créer un nouveau compte
      db.run(`INSERT INTO accounts (user_id, account_name, iban, bank_address, initial_balance, initial_balance_date)
              VALUES (?, ?, ?, ?, ?, ?)`,
              [userId, account_name, iban, bank_address, initial_balance, initial_balance_date],
              callback);
    }
  });
}

// Fonctions pour le pointage des transactions
function getUnreconciledTransactions(userId, callback) {
  // Exclure les transactions futures du pointage
  const today = new Date().toISOString().split('T')[0] + 'T23:59:59.999Z'

  db.all(`SELECT t.id, t.amount, t.type, t.description, t.date_iso, c.id as category_id, c.name as category_name,
    t.is_reconciled, t.reconciled_date
    FROM transactions t LEFT JOIN categories c ON t.category_id = c.id
    WHERE t.user_id = ? AND (t.is_reconciled = 0 OR t.is_reconciled IS NULL) AND t.date_iso <= ?
    ORDER BY date_iso DESC`, [userId, today], callback);
}

function reconcileTransaction(id, userId, callback) {
  db.run('UPDATE transactions SET is_reconciled = 1, reconciled_date = CURRENT_TIMESTAMP, reconciled_by = ? WHERE id = ? AND user_id = ?',
    [userId, id, userId], callback);
}

function isTransactionReconciled(id, userId, callback) {
  db.get('SELECT is_reconciled FROM transactions WHERE id = ? AND user_id = ?', [id, userId], (err, row) => {
    if (err) return callback(err);
    callback(null, row && row.is_reconciled === 1);
  });
}

module.exports = {
  db,
  getAccount,
  createOrUpdateAccount,
  getUnreconciledTransactions,
  reconcileTransaction,
  isTransactionReconciled
};
