# 🚀 Scripts de gestion - Ma Compta Perso

Ce document décrit la nouvelle architecture des scripts et leur utilisation.

## 🏗️ **Architecture des scripts**

Le projet utilise maintenant une architecture claire avec des dossiers séparés :

```
MaComptaPerso/
├── bin/                    # Scripts de fonctionnement quotidien
│   ├── dev                 # Mode développement
│   ├── start               # Démarrage production
│   ├── stop                # Arrêt de l'application
│   ├── restart-client      # Redémarrage client uniquement
│   └── restart-server      # Redémarrage serveur uniquement
├── dev-tools/              # Outils de développement
│   ├── release.sh          # Gestion des releases
│   ├── generate-changelog.js  # Génération changelog
│   └── update-versions.js  # Synchronisation versions
├── deployment/             # Scripts de déploiement
│   ├── install-macompta.sh # Installation automatique
│   ├── start-production.sh # Démarrage production
│   └── stop-production.sh  # Arrêt production
└── Raccourcis à la racine
    ├── dev.sh              # → bin/dev
    ├── start.sh            # → bin/start
    └── stop.sh             # → bin/stop
```

## 📋 **Scripts principaux (usage quotidien)**

### 🚀 Mode développement
```bash
./dev.sh                   # Raccourci
# ou
./bin/dev                  # Script direct
```
- Rechargement automatique avec nodemon
- Logs en temps réel
- Idéal pour le développement

### ▶️ Démarrage production
```bash
./start.sh                 # Raccourci
# ou
./bin/start                # Script direct
```
- Mode production optimisé
- Ouverture automatique du navigateur
- Logs centralisés

### ⏹️ Arrêt de l'application
```bash
./stop.sh                  # Raccourci
# ou
./bin/stop                 # Script direct
```
- Arrêt propre de tous les processus
- Nettoyage des fichiers PID

## 🔧 **Scripts spécialisés**

### Redémarrage partiel
```bash
# Redémarre uniquement le serveur
./bin/restart-server

# Redémarre uniquement le client
./bin/restart-client
```

## 🛠️ **Outils de développement**

### Créer une release
```bash
./dev-tools/release.sh patch    # 1.0.0 → 1.0.1
./dev-tools/release.sh minor    # 1.0.1 → 1.1.0
./dev-tools/release.sh major    # 1.1.0 → 2.0.0
```

### Générer le changelog
```bash
node dev-tools/generate-changelog.js
```

### Synchroniser les versions
```bash
node dev-tools/update-versions.js
```

## 🚀 **Déploiement**

### Installation automatique (utilisateur final)
```bash
./deployment/install-macompta.sh
```

### Démarrage production (depuis le projet)
```bash
./deployment/start-production.sh
```

## 📊 **Surveillance des logs**

### Logs en temps réel
```bash
# Mode développement
tail -f server-dev.log
tail -f client-dev.log

# Mode production
tail -f production.log
tail -f server.log
tail -f client.log
```

### Logs des dernières erreurs
```bash
# Dernières erreurs
tail -20 production.log | grep -i error
tail -20 server.log | grep -i error
```

## 🌐 **URLs de l'application**

- **Application web** : http://localhost:5173
- **API Backend** : http://localhost:3333
- **Login par défaut** : `admin` / `password`

## 💡 **Guide d'utilisation**

### Pour les développeurs
1. **Développement quotidien** : `./dev.sh` (rechargement automatique)
2. **Test en production** : `./start.sh` (mode optimisé)
3. **Arrêt propre** : `./stop.sh` (toujours utiliser plutôt que Ctrl+C)
4. **Surveillance** : `tail -f production.log` pour voir les erreurs

### Pour les utilisateurs finaux
1. **Installation** : `./deployment/install-macompta.sh`
2. **Utilisation** : Double-clic sur l'icône desktop
3. **Mise à jour** : Voir `deployment/README.md`

### Pour les releases
1. **Créer une release** : `./dev-tools/release.sh patch`
2. **Vérifier le changelog** : Consulter `CHANGELOG.md`
3. **Pousser les changements** : Le script propose le push automatique

## 🔧 **Commandes manuelles (dépannage)**

### Démarrage manuel
```bash
# Serveur backend
cd server && npm start

# Client React (développement)
cd client && npm run dev

# Client React (production)
cd client && npx vite preview --port 5173
```

### Arrêt manuel
```bash
# Arrêter tous les processus Node.js
pkill -f "node.*index.js"

# Arrêter tous les processus Vite
pkill -f "vite"
```

## 🐛 **Dépannage**

### Si les ports sont occupés
```bash
# Voir qui utilise les ports
netstat -tlnp | grep -E ":(3333|5173)"

# Forcer l'arrêt
sudo lsof -ti:3333 | xargs kill -9
sudo lsof -ti:5173 | xargs kill -9
```

### Si les scripts ne fonctionnent pas
```bash
# Rendre tous les scripts exécutables
chmod +x bin/* dev-tools/* deployment/*
chmod +x *.sh

# Vérifier les permissions
ls -la bin/ dev-tools/ deployment/
```

### Problèmes de dépendances
```bash
# Réinstaller toutes les dépendances
npm run install:all

# Reconstruire le client
cd client && npm run build && cd ..
```

## 📚 **Documentation détaillée**

- **bin/README.md** - Scripts de fonctionnement quotidien
- **dev-tools/README.md** - Outils de développement et releases
- **deployment/README.md** - Scripts de déploiement et installation
- **docs/IMPLEMENTATION.md** - Guide complet de déploiement
