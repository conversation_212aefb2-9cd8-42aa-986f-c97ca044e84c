name: Auto Changelog

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  update-changelog:
    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, 'chore: release')"
    permissions:
      contents: write
      pull-requests: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
    
    - name: Check if changelog needs update
      id: check
      run: |
        # Vérifier s'il y a de nouveaux commits depuis la dernière mise à jour du changelog
        LAST_CHANGELOG_COMMIT=$(git log -1 --format="%H" -- CHANGELOG.md 2>/dev/null || echo "")
        LATEST_COMMIT=$(git log -1 --format="%H")
        
        if [ "$LAST_CHANGELOG_COMMIT" != "$LATEST_COMMIT" ]; then
          echo "UPDATE_NEEDED=true" >> $GITHUB_OUTPUT
        else
          echo "UPDATE_NEEDED=false" >> $GITHUB_OUTPUT
        fi
    
    - name: Generate changelog
      if: steps.check.outputs.UPDATE_NEEDED == 'true'
      run: |
        # Créer un changelog basique pour les commits récents
        echo "# Changelog" > CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "Tous les changements notables de ce projet seront documentés dans ce fichier." >> CHANGELOG.md
        echo "" >> CHANGELOG.md
        
        # Ajouter les commits récents
        echo "## Derniers changements" >> CHANGELOG.md
        echo "" >> CHANGELOG.md
        git log --oneline --no-merges -10 | while read line; do
          echo "- $line" >> CHANGELOG.md
        done
    
    - name: Commit changelog
      if: steps.check.outputs.UPDATE_NEEDED == 'true'
      run: |
        git add CHANGELOG.md
        git commit -m "docs: update changelog [skip ci]" || exit 0
        git push
