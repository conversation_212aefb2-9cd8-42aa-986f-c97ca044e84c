name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm install
        cd client && npm install
        cd ../server && npm install
    
    - name: Build application
      run: npm run build
    
    - name: Extract version from tag
      id: version
      run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
    
    - name: Generate changelog for this version
      id: changelog
      run: |
        # Obtenir les commits depuis le dernier tag
        PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD~1 2>/dev/null || echo "")
        if [ -z "$PREVIOUS_TAG" ]; then
          COMMITS=$(git log --oneline --no-merges)
        else
          COMMITS=$(git log ${PREVIOUS_TAG}..HEAD --oneline --no-merges)
        fi
        
        # Créer le changelog
        echo "## What's Changed" > release_notes.md
        echo "" >> release_notes.md
        
        # Parser les commits
        echo "$COMMITS" | while read line; do
          if [[ $line =~ ^[a-f0-9]+[[:space:]]+(feat|feature):[[:space:]]*(.+)$ ]]; then
            echo "### ✨ New Features" >> release_notes.md
            echo "- ${BASH_REMATCH[2]}" >> release_notes.md
            echo "" >> release_notes.md
          elif [[ $line =~ ^[a-f0-9]+[[:space:]]+(fix|bug):[[:space:]]*(.+)$ ]]; then
            echo "### 🐛 Bug Fixes" >> release_notes.md
            echo "- ${BASH_REMATCH[2]}" >> release_notes.md
            echo "" >> release_notes.md
          else
            echo "### 🔧 Other Changes" >> release_notes.md
            echo "- ${line#* }" >> release_notes.md
            echo "" >> release_notes.md
          fi
        done
        
        # Nettoyer les doublons de headers
        awk '!seen[$0]++' release_notes.md > temp && mv temp release_notes.md
    
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ steps.version.outputs.VERSION }}
        body_path: release_notes.md
        draft: false
        prerelease: false
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-${{ steps.version.outputs.VERSION }}
        path: |
          client/dist/
          server/
          !server/node_modules/
          !server/data/
